#!/usr/bin/env node
"use strict";
/**
 * @license
 * Copyright 2020 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.bail = exports.getPreamble = exports.opNameToFileName = exports.kernelNameToVariableName = void 0;
var chalk = require("chalk");
/**
 * Normalized kernels names to the variable name used in code for the kernel
 * config.
 */
function kernelNameToVariableName(kernelName) {
    if (kernelName.startsWith('_')) {
        // e.g. _FusedMatMulConfig
        return "_".concat(kernelName.charAt(1).toLowerCase()).concat(kernelName.slice(2));
    }
    return kernelName.charAt(0).toLowerCase() + kernelName.slice(1);
}
exports.kernelNameToVariableName = kernelNameToVariableName;
/**
 * Given an op name returns the name of the file that would export that op.
 */
function opNameToFileName(opName) {
    // add exceptions here.
    if (opName === 'isNaN') {
        return 'is_nan';
    }
    else if (opName.match(/(.*)ND/)) {
        return opName.match(/(.*)ND/)[1].replace(/[A-Z]/g, function (s) { return "_".concat(s.toLowerCase()); }) +
            '_nd';
    }
    else if (opName === 'avgPool3d') {
        return 'avg_pool_3d';
    }
    else if (opName.match(/concat[0-9]d/)) {
        return "concat_".concat(opName.match(/concat([0-9]d)/)[1]);
    }
    return opName.replace(/[A-Z]/g, function (s) { return "_".concat(s.toLowerCase()); });
}
exports.opNameToFileName = opNameToFileName;
function getPreamble() {
    var preamble = "/**\n * @license\n * Copyright ".concat((new Date()).getFullYear(), " Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// This file is autogenerated.\n\n");
    return preamble;
}
exports.getPreamble = getPreamble;
function bail(errorMsg) {
    console.log(chalk.red(errorMsg));
    process.exit(1);
}
exports.bail = bail;
//# sourceMappingURL=util.js.map