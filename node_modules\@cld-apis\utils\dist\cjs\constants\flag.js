"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VFlags = exports.Flags = void 0;
exports.Flags = {
    AnyFormat: "any_format",
    Attachment: "attachment",
    AsPng: "apng",
    AsWebp: "awebp",
    Clip: "clip",
    ClipEvenodd: "clip_evenodd",
    Cutter: "cutter",
    ForceStrip: "force_strip",
    ForceIcc: "force_icc",
    GetInfo: "getinfo",
    IgnoreAspectRatio: "ignore_aspect_ratio",
    IgnoreMaskChannels: "ignore_mask_channels",
    ImmutableCache: "immutable_cache",
    KeepAttribution: "keep_attribution",
    KeepIptc: "keep_iptc",
    LayerApply: "layer_apply",
    Lossy: "lossy",
    NoOverflow: "no_overflow",
    PreserveTransparency: "preserve_transparency",
    PNG8: "png8",
    PNG24: "png24",
    PNG32: "png32",
    Progressive: "progressive",
    Rasterize: "rasterize",
    RegionRelative: "region_relative",
    Relative: 'relative',
    ReplaceImage: 'replace_image',
    Sanitize: 'sanitize',
    StripProfile: 'strip_profile',
    TextNoTrim: 'text_no_trim',
    TextDisallowOverflow: 'text_disallow_overflow',
    Tiff8LZW: 'tiff8_lzw',
    Tiled: 'tiled'
};
exports.VFlags = {
    Animated: "animated",
    AsWebp: "awebp",
    Attachement: "attachment",
    StreamingAttachment: "streaming_attachment",
    GetInfo: "getinfo",
    HLSV3: "hlsv3",
    KeepDar: "keep_dar",
    LayerApply: "layer_apply",
    NoStream: "no_stream",
    Mono: "mono",
    Relative: "relative",
    Splice: "splice",
    Truncate: "truncate_ts",
    Waveform: "waveform",
};
