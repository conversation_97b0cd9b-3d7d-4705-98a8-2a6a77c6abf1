{"name": "@eslint/core", "version": "0.9.1", "description": "Runtime-agnostic core of ESLint", "type": "module", "types": "./dist/esm/types.d.ts", "exports": {"types": {"import": "./dist/esm/types.d.ts", "require": "./dist/cjs/types.d.cts"}}, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"build:cts": "node -e \"fs.cpSync('dist/esm/types.d.ts', 'dist/cjs/types.d.cts')\"", "build": "tsc && npm run build:cts", "test:jsr": "npx jsr@latest publish --dry-run", "test:types": "tsc -p tests/types/tsconfig.json"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git"}, "keywords": ["eslint", "core"], "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "homepage": "https://github.com/eslint/rewrite#readme", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"json-schema": "^0.4.0", "typescript": "^5.4.5"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}