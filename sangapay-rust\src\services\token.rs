use anyhow::{Result, anyhow, Context};
use hmac::{Hmac, <PERSON>};
use rand::Rng;
use serde::{Serialize, Deserialize};
use sha2::Sha256;
use std::env;
use std::collections::HashSet;
use std::time::{SystemTime, UNIX_EPOCH};

/// Permission type
pub type Permission = String;

/// Token types
#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Clone, Copy)]
#[serde(rename_all = "lowercase")]
pub enum TokenType {
    Grant,
    Access,
    Refresh,
}

impl std::fmt::Display for TokenType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TokenType::Grant => write!(f, "grant"),
            TokenType::Access => write!(f, "access"),
            TokenType::Refresh => write!(f, "refresh"),
        }
    }
}

/// Access token response
#[derive(Debug, Serialize, Deserialize)]
pub struct AccessTokenResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub token_type: String,
    pub expires_in: u64,
}

/// Decoded token information
#[derive(Debug, Clone)]
pub struct DecodedToken {
    pub user_id: String,
    pub client_id: Option<String>,
    pub permissions: Vec<Permission>,
    pub expiration_time: i64,
    pub token_type: TokenType,
    pub payload: serde_json::Value,
}

// No additional methods needed for DecodedToken

/// Token service for authentication
pub struct TokenService {
    secret_key: String,
    internal_call_key: String,
    one_trillion_years_in_ms: i64,
}



impl TokenService {
    /// Create a new token service
    pub fn new() -> Result<Self> {
        let secret_key = env::var("SECRET_KEY")
            .with_context(|| "SECRET_KEY environment variable is not defined")?;

        // Generate a secure random key for internal calls
        let internal_call_key = "INTERNAL_CALL".to_string();

        // Use max i64 value for effectively infinite duration
        // This is approximately 292 years, which is more than sufficient
        let one_trillion_years_in_ms = i64::MAX;

        Ok(Self {
            secret_key,
            internal_call_key,
            one_trillion_years_in_ms,
        })
    }

    /// Get the internal call key
    pub fn internal_call_key(&self) -> &str {
        &self.internal_call_key
    }

    /// Generate a secure random 6-digit user ID
    fn generate_secure_random_user_id(&self) -> String {
        let min = 100000; // Smallest 6-digit number
        let max = 999999; // Largest 6-digit number
        rand::thread_rng().gen_range(min..=max).to_string()
    }

    /// Generate a salt
    fn generate_salt(&self) -> String {
        let salt_bytes: [u8; 16] = rand::thread_rng().gen();
        hex::encode(salt_bytes)
    }

    /// Create a checksum for data integrity
    fn create_checksum(&self, data: &str) -> String {
        let mut mac = Hmac::<Sha256>::new_from_slice(self.secret_key.as_bytes())
            .expect("HMAC can take key of any size");
        mac.update(data.as_bytes());
        let result = mac.finalize();
        // Truncate to 16 characters (32 hex chars) 
        let full_hex = hex::encode(result.into_bytes());
        full_hex[..16].to_string()
    }

    /// Generate a grant token
    pub fn generate_grant_token(
        &self,
        payload: serde_json::Value,
        expires_in: i64,
        permissions: Vec<Permission>,
        internal_key: &str,
        token_type: TokenType,
    ) -> Result<String> {
        // Verify internal key
        let is_internal_call = internal_key == self.internal_call_key;

        // Ensure expiresIn is greater than 0 unless it's an internal call
        if !is_internal_call && expires_in <= 0 {
            return Err(anyhow!("expiresIn is required and must be greater than 0"));
        } else if !is_internal_call && expires_in > 600 {
            return Err(anyhow!("expiresIn must be less than or equal to 10 minutes (600 seconds)"));
        }

        let user_id = self.generate_secure_random_user_id();
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as i64;

        // Calculate expiration time
        let expiration_time = timestamp + expires_in * 1000;

        // Merge payload, permissions, expiration time, and token type
        let mut payload_with_expiry = payload.clone();
        payload_with_expiry["_expiry"] = serde_json::json!(expiration_time);
        payload_with_expiry["permissions"] = serde_json::json!(permissions);
        payload_with_expiry["tokenType"] = serde_json::json!(token_type);

        // Encode the payload as a hexadecimal string
        let encoded_payload_with_expiry = hex::encode(
            serde_json::to_string(&payload_with_expiry)
                .with_context(|| "Failed to serialize payload")?
                .as_bytes()
        );

        // Combine userId and encoded payload
        let user_id_and_payload = format!("{}_{}",user_id, encoded_payload_with_expiry);

        // Generate a salt
        let salt = self.generate_salt();

        // Create a checksum for data integrity - match the TypeScript implementation format
        let checksum = self.create_checksum(&format!("{}_{}.{}", user_id, encoded_payload_with_expiry, salt));

        // Combine all parts to create the token
        let token = format!("{}.{}_{}",user_id_and_payload, salt, checksum);

        Ok(token)
    }

    /// Generate access and refresh tokens
    pub fn generate_access_token(
        &self,
        grant_type: &str,
        client_id: &str,
        client_secret: &str,
        grant_token: &str,
    ) -> Result<(String, String)> {
        // Validate grant_type
        if grant_type != "authorization_code" {
            return Err(anyhow!("Invalid grant_type. Expected \"authorization_code\""));
        }

        // Validate client_id and client_secret
        if client_id.is_empty() || client_secret.is_empty() {
            return Err(anyhow!("client_id and client_secret are required"));
        }

        // Validate grant token
        let decoded_token = self.validate_grant_token(grant_token)?;

        // Verify token type
        if decoded_token.token_type != TokenType::Grant {
            return Err(anyhow!("Invalid token type. Expected a grant token"));
        }

        // Generate an access token (expires in 1 hour)
        let access_token = self.generate_grant_token(
            serde_json::json!({
                "userId": decoded_token.user_id,
                "client_id": client_id,
            }),
            3600, // 1 hour in seconds
            decoded_token.permissions.clone(),
            &self.internal_call_key,
            TokenType::Access,
        )?;

        // Generate a refresh token (long-lived)
        let refresh_token = self.generate_grant_token(
            serde_json::json!({
                "userId": decoded_token.user_id,
                "client_id": client_id,
            }),
            self.one_trillion_years_in_ms / 1000, // Very long-lived token
            decoded_token.permissions,
            &self.internal_call_key,
            TokenType::Refresh,
        )?;

        Ok((access_token, refresh_token))
    }

    /// Validate a grant token
    pub fn validate_grant_token(&self, token: &str) -> Result<DecodedToken> {
        if token.is_empty() {
            return Err(anyhow!("Token is missing"));
        }

        // Split token into parts
        let token_parts: Vec<&str> = token.split('.').collect();
        if token_parts.len() != 2 {
            return Err(anyhow!("Invalid token"));
        }

        let user_id_and_payload = token_parts[0];
        let salt_and_checksum = token_parts[1];

        // Split userId and payload
        let user_id_payload_parts: Vec<&str> = user_id_and_payload.split('_').collect();
        if user_id_payload_parts.len() < 2 {
            return Err(anyhow!("Invalid token: Missing userId or payload"));
        }

        let user_id = user_id_payload_parts[0];
        let encoded_payload_with_expiry = user_id_payload_parts[1..].join("_");

        // Split salt and checksum
        let salt_checksum_parts: Vec<&str> = salt_and_checksum.split('_').collect();
        if salt_checksum_parts.len() != 2 {
            return Err(anyhow!("Invalid token: Missing salt or checksum"));
        }

        let salt = salt_checksum_parts[0];
        let checksum = salt_checksum_parts[1];

        // Verify the checksum - match the TypeScript implementation format
        let expected_checksum = self.create_checksum(&format!("{}_{}.{}", user_id, encoded_payload_with_expiry, salt));
        if checksum != expected_checksum {
            return Err(anyhow!("Invalid token: Checksum mismatch"));
        }

        // Decode the payload with expiry
        let payload_bytes = match hex::decode(&encoded_payload_with_expiry) {
            Ok(bytes) => bytes,
            Err(_) => return Err(anyhow!("Invalid token: Failed to decode payload")),
        };

        let payload_str = match String::from_utf8(payload_bytes) {
            Ok(s) => s,
            Err(_) => return Err(anyhow!("Invalid token: Payload is not valid UTF-8")),
        };

        let payload_with_expiry: serde_json::Value = match serde_json::from_str(&payload_str) {
            Ok(json) => json,
            Err(_) => return Err(anyhow!("Invalid token: Payload is not valid JSON")),
        };

        // Extract the expiration time, permissions, and token type
        let expiration_time = match payload_with_expiry.get("_expiry") {
            Some(exp) => match exp.as_i64() {
                Some(e) => e,
                None => return Err(anyhow!("Invalid token: Expiration time is not a valid number")),
            },
            None => return Err(anyhow!("Invalid token: Missing expiration time")),
        };

        // Check if the token has expired
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as i64;

        if current_time > expiration_time {
            return Err(anyhow!("Token has expired"));
        }

        // Extract permissions
        let permissions = match payload_with_expiry.get("permissions") {
            Some(perms) => {
                match perms.as_array() {
                    Some(arr) => {
                        arr.iter()
                            .filter_map(|p| p.as_str().map(|s| s.to_string()))
                            .collect()
                    },
                    None => Vec::new(),
                }
            },
            None => Vec::new(),
        };

        // Extract token type
        let token_type = match payload_with_expiry.get("tokenType") {
            Some(tt) => {
                match tt.as_str() {
                    Some("grant") => TokenType::Grant,
                    Some("access") => TokenType::Access,
                    Some("refresh") => TokenType::Refresh,
                    _ => return Err(anyhow!("Invalid token: Unknown token type")),
                }
            },
            None => return Err(anyhow!("Invalid token: Missing token type")),
        };

        // Extract client_id
        let client_id = payload_with_expiry.get("client_id")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        // Create a copy of the payload without the special fields
        let mut payload = payload_with_expiry.clone();
        payload.as_object_mut().map(|obj| {
            obj.remove("_expiry");
            obj.remove("permissions");
            obj.remove("tokenType");
        });

        // Create decoded token
        let decoded_token = DecodedToken {
            user_id: user_id.to_string(),
            client_id,
            permissions,
            expiration_time,
            token_type,
            payload,
        };

        Ok(decoded_token)
    }

    /// Refresh an access token
    pub fn refresh_access_token(
        &self,
        refresh_token: &str,
        client_id: &str,
        client_secret: &str,
        grant_type: &str,
    ) -> Result<String> {
        // Validate grant_type
        if grant_type != "refresh_token" {
            return Err(anyhow!("Invalid grant_type. Expected \"refresh_token\""));
        }

        // Validate client_id and client_secret
        if client_id.is_empty() || client_secret.is_empty() {
            return Err(anyhow!("client_id and client_secret are required"));
        }

        // Validate refresh token
        let decoded_token = self.validate_grant_token(refresh_token)?;

        // Verify token type
        if decoded_token.token_type != TokenType::Refresh {
            return Err(anyhow!("Invalid token type. Expected a refresh token"));
        }

        // Check if the client_id matches
        let token_client_id = decoded_token.payload.get("client_id")
            .and_then(|v| v.as_str())
            .unwrap_or("");

        if token_client_id != client_id {
            return Err(anyhow!("Invalid refresh token: Client ID mismatch"));
        }

        // Generate a new access token
        let access_token = self.generate_grant_token(
            serde_json::json!({
                "userId": decoded_token.user_id,
                "client_id": client_id,
            }),
            3600, // 1 hour in seconds
            decoded_token.permissions,
            &self.internal_call_key,
            TokenType::Access,
        )?;

        Ok(access_token)
    }

    /// Check if a token has a specific permission
    pub fn has_permission(&self, token: &DecodedToken, required_permission: &str) -> bool {
        token.permissions.contains(&required_permission.to_string())
    }

    /// Check if a token has all required permissions
    pub fn has_all_permissions(&self, token: &DecodedToken, required_permissions: &[&str]) -> bool {
        let token_permissions: HashSet<String> = token.permissions.iter().cloned().collect();
        let required: HashSet<String> = required_permissions.iter().map(|&s| s.to_string()).collect();

        required.is_subset(&token_permissions)
    }

    /// Check if a token has any of the required permissions
    pub fn has_any_permission(&self, token: &DecodedToken, required_permissions: &[&str]) -> bool {
        let token_permissions: HashSet<String> = token.permissions.iter().cloned().collect();
        let required: HashSet<String> = required_permissions.iter().map(|&s| s.to_string()).collect();

        !required.is_disjoint(&token_permissions)
    }
}





