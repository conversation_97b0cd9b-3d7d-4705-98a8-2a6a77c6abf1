export declare const Flags: {
    readonly AnyFormat: "any_format";
    readonly Attachment: "attachment";
    readonly AsPng: "apng";
    readonly AsWebp: "awebp";
    readonly Clip: "clip";
    readonly ClipEvenodd: "clip_evenodd";
    readonly Cutter: "cutter";
    readonly ForceStrip: "force_strip";
    readonly ForceIcc: "force_icc";
    readonly GetInfo: "getinfo";
    readonly IgnoreAspectRatio: "ignore_aspect_ratio";
    readonly IgnoreMaskChannels: "ignore_mask_channels";
    readonly ImmutableCache: "immutable_cache";
    readonly KeepAttribution: "keep_attribution";
    readonly KeepIptc: "keep_iptc";
    readonly LayerApply: "layer_apply";
    readonly Lossy: "lossy";
    readonly NoOverflow: "no_overflow";
    readonly PreserveTransparency: "preserve_transparency";
    readonly PNG8: "png8";
    readonly PNG24: "png24";
    readonly PNG32: "png32";
    readonly Progressive: "progressive";
    readonly Rasterize: "rasterize";
    readonly RegionRelative: "region_relative";
    readonly Relative: "relative";
    readonly ReplaceImage: "replace_image";
    readonly Sanitize: "sanitize";
    readonly StripProfile: "strip_profile";
    readonly TextNoTrim: "text_no_trim";
    readonly TextDisallowOverflow: "text_disallow_overflow";
    readonly Tiff8LZW: "tiff8_lzw";
    readonly Tiled: "tiled";
};
export declare const VFlags: {
    readonly Animated: "animated";
    readonly AsWebp: "awebp";
    readonly Attachement: "attachment";
    readonly StreamingAttachment: "streaming_attachment";
    readonly GetInfo: "getinfo";
    readonly HLSV3: "hlsv3";
    readonly KeepDar: "keep_dar";
    readonly LayerApply: "layer_apply";
    readonly NoStream: "no_stream";
    readonly Mono: "mono";
    readonly Relative: "relative";
    readonly Splice: "splice";
    readonly Truncate: "truncate_ts";
    readonly Waveform: "waveform";
};
