{"name": "@tensorflow/tfjs-data", "version": "4.22.0", "description": "TensorFlow Data API in JavaScript", "private": false, "main": "dist/tf-data.node.js", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs.git", "directory": "tfjs-data"}, "jsdelivr": "dist/tf-data.min.js", "unpkg": "dist/tf-data.min.js", "types": "dist/index.d.ts", "jsnext:main": "dist/index.js", "module": "dist/index.js", "miniprogram": "dist/miniprogram", "license": "Apache-2.0", "devDependencies": {"@bazel/bazelisk": "^1.12.0", "@bazel/ibazel": "^0.16.2"}, "scripts": {"build": "bazel build :tfjs-data_pkg", "publish-npm": "bazel run :tfjs-data_pkg.publish", "test": "bazel test :tests", "test-dev": "ibazel test :tests", "test-debug": "yarn test-node-debug", "test-browser": "bazel test :tfjs-data_browser_test --test-output=streamed", "test-browser-debug": "bazel run :tfjs-data_browser_test --test-output=streamed", "test-node": "bazel test --test_output=streamed :tfjs-data_test", "test-node-debug": "bazel run --config=debug --test_output=streamed :tfjs-data_test"}, "peerDependencies": {"@tensorflow/tfjs-core": "4.22.0", "seedrandom": "^3.0.5"}, "dependencies": {"@types/node-fetch": "^2.1.2", "node-fetch": "~2.6.1", "string_decoder": "^1.3.0"}, "browser": {"fs": false, "node-fetch": false, "string_decoder": false, "crypto": false}}