# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [0.2.0](https://github.com/mayashavin/cloudinary-api/compare/@cld-apis/utils@0.1.5...@cld-apis/utils@0.2.0) (2022-01-18)


### Features

* replaced lodash.snakecase with snake-case [#24](https://github.com/mayashavin/cloudinary-api/issues/24) ([5af2b05](https://github.com/mayashavin/cloudinary-api/commit/5af2b05e5af300c9b26b7f8e93c984d5735bcb7a))





## [0.1.5](https://github.com/mayashavin/cloudinary-api/compare/@cld-apis/utils@0.1.4...@cld-apis/utils@0.1.5) (2022-01-18)

**Note:** Version bump only for package @cld-apis/utils





## [0.1.4](https://github.com/mayashavin/cloudinary-api/compare/@cld-apis/utils@0.1.3...@cld-apis/utils@0.1.4) (2022-01-10)

**Note:** Version bump only for package @cld-apis/utils





## [0.1.3](https://github.com/mayashavin/cloudinary-api/compare/@cld-apis/utils@0.1.2...@cld-apis/utils@0.1.3) (2021-02-03)

**Note:** Version bump only for package @cld-apis/utils





## 0.1.2 (2021-02-03)


### Bug Fixes

* add colorify and artistic filters ([c2409ab](https://github.com/mayashavin/cloudinary-api/commit/c2409abd302388307813b1bfe79843d1c081fa1c))
* add more types for server api ([9548f34](https://github.com/mayashavin/cloudinary-api/commit/9548f34a7a4a662e8bdd991dc1d61b864c3f2d03))
