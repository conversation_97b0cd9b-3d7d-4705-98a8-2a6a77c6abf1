use serde::{Deserialize, Serialize};
use mongodb::bson::{self, oid::ObjectId, DateTime};
use chrono::{DateTime as ChronoDateTime, Utc};

/// OAuth Client Model
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OAuthClient {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,

    #[serde(rename = "clientId")]
    pub client_id: String,

    #[serde(rename = "clientSecret")]
    pub client_secret: Option<String>,

    #[serde(rename = "clientName")]
    pub client_name: String,

    #[serde(rename = "clientType")]
    pub client_type: String, // confidential, public

    #[serde(rename = "userId")]
    pub user_id: ObjectId,

    #[serde(rename = "redirectUris")]
    pub redirect_uris: Vec<String>,

    pub description: Option<String>,

    pub website: Option<String>,

    #[serde(rename = "isActive")]
    pub is_active: bool,

    #[serde(rename = "createdAt")]
    pub created_at: ChronoDateTime<Utc>,

    #[serde(rename = "updatedAt")]
    pub updated_at: ChronoDateTime<Utc>,
}

/// Authorization Code model for OAuth 2.0
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OAuthCode {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,

    pub code: String,

    #[serde(rename = "clientId")]
    pub client_id: String,

    #[serde(rename = "userId")]
    pub user_id: ObjectId,

    #[serde(rename = "redirectUri")]
    pub redirect_uri: String,

    pub scopes: Vec<String>,

    pub permissions: Vec<String>,

    #[serde(rename = "expiresAt")]
    pub expires_at: ChronoDateTime<Utc>,

    #[serde(rename = "createdAt")]
    pub created_at: ChronoDateTime<Utc>,

    pub used: bool,
}

/// OAuth Token model for access and refresh tokens
#[derive(Debug, Serialize, Deserialize)]
pub struct OAuthToken {
    #[serde(rename = "_id")]
    pub id: ObjectId,
    
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub client_id: String,
    pub user_id: ObjectId,
    pub scopes: Vec<String>,
    pub token_type: String, // "access" or "refresh"
    pub expires_at: DateTime,
    pub created_at: DateTime,
    pub is_revoked: bool,
    pub revoked_at: Option<DateTime>,
}

/// Token Blacklist model for revoked tokens
#[derive(Debug, Serialize, Deserialize)]
pub struct TokenBlacklist {
    #[serde(rename = "_id")]
    pub id: ObjectId,
    
    pub token: String,
    pub token_type: String, // "access", "refresh", or "grant"
    pub expires_at: DateTime,
    pub revoked_at: DateTime,
    pub revoked_by: Option<ObjectId>,
    pub reason: Option<String>,
}

impl AuthorizationCode {
    /// Check if the authorization code is valid
    pub fn is_valid(&self) -> bool {
        !self.is_used && self.expires_at > mongodb::bson::DateTime::now()
    }
}

impl OAuthToken {
    /// Check if the token is valid
    pub fn is_valid(&self) -> bool {
        !self.is_revoked && self.expires_at > mongodb::bson::DateTime::now()
    }
    
    /// Check if the token has a specific scope
    pub fn has_scope(&self, scope: &str) -> bool {
        self.scopes.contains(&scope.to_string())
    }
}
