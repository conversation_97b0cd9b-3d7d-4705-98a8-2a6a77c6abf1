import { Request, Response } from 'express';
import mongoose from 'mongoose';
import validator from 'validator';
import User from '../models/user.model';
import AccountNumber from '../models/accountnumber.model';
import Transaction from '../models/transaction.model';
import { sanitizeInput } from '../utils/security.utils';
import axios from 'axios';

// Pagination interface
interface PaginationQuery {
  page?: string;
  limit?: string;
  cursor?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

// Standard pagination response
interface PaginatedResponse<T> {
  status: boolean;
  message: string;
  data: T[];
  meta: {
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
      nextCursor?: string;
      prevCursor?: string;
    };
  };
}

// Helper function to build pagination metadata
const buildPaginationMeta = (
  page: number,
  limit: number,
  total: number,
  data: any[],
  cursorField?: string
) => {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  const meta: any = {
    page,
    limit,
    total,
    totalPages,
    hasNext,
    hasPrev,
  };

  // Add cursor-based pagination if cursor field is provided
  if (cursorField && data.length > 0) {
    meta.nextCursor = hasNext ? data[data.length - 1][cursorField] : null;
    meta.prevCursor = hasPrev ? data[0][cursorField] : null;
  }

  return meta;
};

// Webhook functionality
const triggerWebhook = async (eventType: string, data: any, clientId: string) => {
  try {
    // Find webhook URLs for the client
    const WebhookConfig = require('../models/webhook.model');
    const webhooks = await WebhookConfig.find({
      clientId,
      isActive: true,
      events: eventType
    });

    for (const webhook of webhooks) {
      try {
        const payload = {
          event: eventType,
          data,
          timestamp: new Date().toISOString(),
          clientId,
        };

        await axios.post(webhook.url, payload, {
          headers: {
            'Content-Type': 'application/json',
            'X-Webhook-Signature': generateWebhookSignature(payload, webhook.secret),
            'User-Agent': 'SangaPay-Webhook/1.0',
          },
          timeout: 10000, // 10 seconds timeout
        });

        // Log successful webhook delivery
        console.log(`Webhook delivered successfully: ${eventType} to ${webhook.url}`);
      } catch (error) {
        // Log failed webhook delivery
        console.error(`Webhook delivery failed: ${eventType} to ${webhook.url}`, error);

        // Store failed webhook for retry
        const FailedWebhook = require('../models/failed-webhook.model');
        await FailedWebhook.create({
          webhookId: webhook._id,
          eventType,
          payload: { event: eventType, data, timestamp: new Date().toISOString(), clientId },
          error: (error as Error).message,
          retryCount: 0,
          nextRetry: new Date(Date.now() + 60000), // Retry in 1 minute
        });
      }
    }
  } catch (error) {
    console.error('Webhook trigger error:', error);
  }
};

const generateWebhookSignature = (payload: any, secret: string): string => {
  const crypto = require('crypto');
  const payloadString = JSON.stringify(payload);
  return crypto.createHmac('sha256', secret).update(payloadString).digest('hex');
};

/**
 * Developer API: Validate Account
 * Validates if an account number exists and returns account details
 */
export const validateAccount = async (req: Request, res: Response): Promise<any> => {
  try {
    const { accountNumber } = req.params;
    
    if (!accountNumber) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Account number is required.',
          suggestions: ['Provide a valid account number in the URL path.'],
        },
      });
    }

    const sanitizedAccountNumber = sanitizeInput(accountNumber);

    // Find the account
    const account = await AccountNumber.findOne({ 
      accountNumber: sanitizedAccountNumber 
    }).populate('userId', 'firstName lastName email phone userIsVerified kyc_status');

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account found with the provided account number.',
          suggestions: ['Verify the account number and try again.'],
        },
      });
    }

    // Return account validation details
    return res.status(200).json({
      status: true,
      message: 'ACCOUNT_VALIDATED',
      data: {
        accountNumber: account.accountNumber,
        accountName: `${account.userId.firstName} ${account.userId.lastName}`,
        accountType: account.accountType,
        currency: account.currency,
        isActive: account.isActive,
        isVerified: account.userId.userIsVerified,
        kycStatus: account.userId.kyc_status,
        createdAt: account.createdAt,
      },
    });
  } catch (error) {
    console.error('Account validation error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while validating the account.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Push Funds
 * Credits funds to a user's account
 */
export const pushFunds = async (req: Request, res: Response): Promise<any> => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { 
      accountNumber, 
      amount, 
      currency, 
      reference, 
      description,
      metadata 
    } = req.body;

    // Validate required fields
    if (!accountNumber || !amount || !currency || !reference) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Missing required fields: accountNumber, amount, currency, reference.',
          suggestions: ['Provide all required fields in the request body.'],
        },
      });
    }

    // Sanitize inputs
    const sanitizedAccountNumber = sanitizeInput(accountNumber);
    const sanitizedCurrency = sanitizeInput(currency);
    const sanitizedReference = sanitizeInput(reference);
    const sanitizedDescription = description ? sanitizeInput(description) : 'Fund credit via API';

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({
        status: false,
        message: 'INVALID_AMOUNT',
        meta: {
          error: 'Amount must be a positive number.',
          suggestions: ['Provide a valid amount greater than 0.'],
        },
      });
    }

    // Find the account
    const account = await AccountNumber.findOne({ 
      accountNumber: sanitizedAccountNumber 
    }).session(session);

    if (!account) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account found with the provided account number.',
          suggestions: ['Verify the account number and try again.'],
        },
      });
    }

    // Check if account is active
    if (!account.isActive) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({
        status: false,
        message: 'ACCOUNT_INACTIVE',
        meta: {
          error: 'Cannot credit funds to an inactive account.',
          suggestions: ['Contact support to activate the account.'],
        },
      });
    }

    // Find or create balance for the currency
    let balanceIndex = account.balance.findIndex((b: any) => b.currency === sanitizedCurrency);
    
    if (balanceIndex === -1) {
      // Create new balance entry for this currency
      account.balance.push({
        currency: sanitizedCurrency,
        walletBalance: amount,
        ledgerBalance: amount,
      });
    } else {
      // Update existing balance
      account.balance[balanceIndex].walletBalance += amount;
      account.balance[balanceIndex].ledgerBalance += amount;
    }

    await account.save({ session });

    // Create transaction record
    const transaction = new Transaction({
      type: 'DEPOSIT',
      amount,
      currency: sanitizedCurrency,
      status: 'COMPLETED',
      reference: sanitizedReference,
      description: sanitizedDescription,
      recipient: {
        userId: account.userId,
        accountNumber: sanitizedAccountNumber,
        name: `${account.userId.firstName} ${account.userId.lastName}`,
      },
      metadata: {
        source: 'DEVELOPER_API',
        clientId: req.clientId,
        ...metadata,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await transaction.save({ session });

    await session.commitTransaction();
    session.endSession();

    return res.status(200).json({
      status: true,
      message: 'FUNDS_CREDITED',
      data: {
        transactionId: transaction._id,
        accountNumber: sanitizedAccountNumber,
        amount,
        currency: sanitizedCurrency,
        reference: sanitizedReference,
        newBalance: account.balance.find((b: any) => b.currency === sanitizedCurrency)?.walletBalance,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    console.error('Push funds error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while processing the fund credit.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Get Account Balance
 * Retrieves account balance for specific currencies
 */
export const getAccountBalance = async (req: Request, res: Response): Promise<any> => {
  try {
    const { accountNumber } = req.params;
    const { currencies } = req.query;

    if (!accountNumber) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Account number is required.',
          suggestions: ['Provide a valid account number in the URL path.'],
        },
      });
    }

    const sanitizedAccountNumber = sanitizeInput(accountNumber);

    // Find the account
    const account = await AccountNumber.findOne({
      accountNumber: sanitizedAccountNumber
    }).populate('userId', 'firstName lastName');

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account found with the provided account number.',
          suggestions: ['Verify the account number and try again.'],
        },
      });
    }

    // Filter balances by currencies if specified
    let balances = account.balance;
    if (currencies) {
      const requestedCurrencies = (currencies as string).split(',').map(c => c.trim().toUpperCase());
      balances = account.balance.filter((b: any) => requestedCurrencies.includes(b.currency));
    }

    return res.status(200).json({
      status: true,
      message: 'BALANCE_RETRIEVED',
      data: {
        accountNumber: sanitizedAccountNumber,
        accountName: `${account.userId.firstName} ${account.userId.lastName}`,
        balances: balances.map((b: any) => ({
          currency: b.currency,
          walletBalance: b.walletBalance,
          ledgerBalance: b.ledgerBalance,
          lastUpdated: b.updatedAt || account.updatedAt,
        })),
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Get balance error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while retrieving account balance.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Get Transactions with Pagination
 * Retrieves paginated transaction history
 */
export const getTransactions = async (req: Request, res: Response): Promise<any> => {
  try {
    const { accountNumber } = req.params;
    const {
      page = '1',
      limit = '20',
      cursor,
      sort = 'createdAt',
      order = 'desc',
      status,
      type,
      currency,
      startDate,
      endDate,
    }: PaginationQuery & {
      status?: string;
      type?: string;
      currency?: string;
      startDate?: string;
      endDate?: string;
    } = req.query;

    if (!accountNumber) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Account number is required.',
          suggestions: ['Provide a valid account number in the URL path.'],
        },
      });
    }

    const sanitizedAccountNumber = sanitizeInput(accountNumber);
    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 20)); // Max 100 per page

    // Find the account first
    const account = await AccountNumber.findOne({
      accountNumber: sanitizedAccountNumber
    });

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account found with the provided account number.',
          suggestions: ['Verify the account number and try again.'],
        },
      });
    }

    // Build query filters
    const query: any = {
      $or: [
        { 'sender.accountNumber': sanitizedAccountNumber },
        { 'recipient.accountNumber': sanitizedAccountNumber },
      ],
    };

    // Add optional filters
    if (status) query.status = sanitizeInput(status);
    if (type) query.type = sanitizeInput(type);
    if (currency) query.currency = sanitizeInput(currency);

    // Date range filter
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Cursor-based pagination
    if (cursor) {
      const cursorDate = new Date(cursor);
      if (order === 'desc') {
        query.createdAt = { ...query.createdAt, $lt: cursorDate };
      } else {
        query.createdAt = { ...query.createdAt, $gt: cursorDate };
      }
    }

    // Get total count for pagination metadata
    const total = await Transaction.countDocuments(query);

    // Build sort object
    const sortObj: any = {};
    sortObj[sort] = order === 'desc' ? -1 : 1;

    // Execute query with pagination
    const transactions = await Transaction.find(query)
      .sort(sortObj)
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum)
      .lean();

    // Transform transactions to include direction
    const transformedTransactions = transactions.map((tx: any) => ({
      id: tx._id,
      type: tx.type,
      amount: tx.amount,
      currency: tx.currency,
      status: tx.status,
      reference: tx.reference,
      description: tx.description || '',
      direction: tx.sender?.accountNumber === sanitizedAccountNumber ? 'DEBIT' : 'CREDIT',
      counterparty: tx.sender?.accountNumber === sanitizedAccountNumber
        ? {
            accountNumber: tx.recipient?.accountNumber,
            name: tx.recipient?.name,
          }
        : {
            accountNumber: tx.sender?.accountNumber,
            name: tx.sender?.name,
          },
      metadata: tx.metadata || {},
      createdAt: tx.createdAt,
      updatedAt: tx.updatedAt,
    }));

    const paginationMeta = buildPaginationMeta(
      pageNum,
      limitNum,
      total,
      transformedTransactions,
      'createdAt'
    );

    const response: PaginatedResponse<any> = {
      status: true,
      message: 'TRANSACTIONS_RETRIEVED',
      data: transformedTransactions,
      meta: {
        pagination: paginationMeta,
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('Get transactions error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while retrieving transactions.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Reconciliation Report
 * Generates reconciliation report for a specific date range
 */
export const getReconciliationReport = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      startDate,
      endDate,
      currency,
      page = '1',
      limit = '50',
    } = req.query as {
      startDate: string;
      endDate: string;
      currency?: string;
      page?: string;
      limit?: string;
    };

    if (!startDate || !endDate) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Start date and end date are required.',
          suggestions: ['Provide startDate and endDate in ISO format (YYYY-MM-DD).'],
        },
      });
    }

    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 50));

    const start = new Date(startDate);
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // Include the entire end date

    // Validate date range
    if (start >= end) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_DATE_RANGE',
        meta: {
          error: 'Start date must be before end date.',
          suggestions: ['Provide a valid date range.'],
        },
      });
    }

    // Build query
    const query: any = {
      createdAt: { $gte: start, $lte: end },
      status: 'COMPLETED',
    };

    if (currency) {
      query.currency = sanitizeInput(currency);
    }

    // Get transactions for the period
    const transactions = await Transaction.find(query)
      .sort({ createdAt: 1 })
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum)
      .lean();

    const total = await Transaction.countDocuments(query);

    // Calculate summary statistics
    const summaryQuery = [
      { $match: query },
      {
        $group: {
          _id: {
            currency: '$currency',
            type: '$type',
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 },
        },
      },
      {
        $group: {
          _id: '$_id.currency',
          types: {
            $push: {
              type: '$_id.type',
              totalAmount: '$totalAmount',
              count: '$count',
            },
          },
          totalTransactions: { $sum: '$count' },
          totalVolume: { $sum: '$totalAmount' },
        },
      },
    ];

    const summary = await Transaction.aggregate(summaryQuery);

    // Calculate opening and closing balances for each currency
    const balanceQuery = currency
      ? { currency: sanitizeInput(currency) }
      : {};

    const accounts = await AccountNumber.find(balanceQuery).lean();

    const balanceSummary = accounts.reduce((acc: any, account: any) => {
      account.balance.forEach((bal: any) => {
        if (!currency || bal.currency === currency) {
          if (!acc[bal.currency]) {
            acc[bal.currency] = {
              totalWalletBalance: 0,
              totalLedgerBalance: 0,
              accountCount: 0,
            };
          }
          acc[bal.currency].totalWalletBalance += bal.walletBalance;
          acc[bal.currency].totalLedgerBalance += bal.ledgerBalance;
          acc[bal.currency].accountCount++;
        }
      });
      return acc;
    }, {});

    const paginationMeta = buildPaginationMeta(
      pageNum,
      limitNum,
      total,
      transactions,
      'createdAt'
    );

    return res.status(200).json({
      status: true,
      message: 'RECONCILIATION_REPORT_GENERATED',
      data: {
        period: {
          startDate: start.toISOString(),
          endDate: end.toISOString(),
        },
        summary: {
          currencies: summary,
          currentBalances: balanceSummary,
        },
        transactions: transactions.map((tx: any) => ({
          id: tx._id,
          type: tx.type,
          amount: tx.amount,
          currency: tx.currency,
          status: tx.status,
          reference: tx.reference,
          sender: tx.sender,
          recipient: tx.recipient,
          metadata: tx.metadata || {},
          createdAt: tx.createdAt,
        })),
        generatedAt: new Date().toISOString(),
        generatedBy: (req as any).clientId,
      },
      meta: {
        pagination: paginationMeta,
      },
    });
  } catch (error) {
    console.error('Reconciliation report error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while generating reconciliation report.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Get Account List with Pagination
 * Retrieves paginated list of accounts
 */
export const getAccounts = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page = '1',
      limit = '20',
      cursor,
      sort = 'createdAt',
      order = 'desc',
      currency,
      isActive,
      search,
    }: PaginationQuery & {
      currency?: string;
      isActive?: string;
      search?: string;
    } = req.query;

    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 20));

    // Build query
    const query: any = {};

    if (currency) {
      query['balance.currency'] = sanitizeInput(currency);
    }

    if (isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Search functionality
    if (search) {
      const searchTerm = sanitizeInput(search);
      query.$or = [
        { accountNumber: { $regex: searchTerm, $options: 'i' } },
        { 'userId.firstName': { $regex: searchTerm, $options: 'i' } },
        { 'userId.lastName': { $regex: searchTerm, $options: 'i' } },
        { 'userId.email': { $regex: searchTerm, $options: 'i' } },
      ];
    }

    // Cursor-based pagination
    if (cursor) {
      const cursorDate = new Date(cursor);
      if (order === 'desc') {
        query.createdAt = { $lt: cursorDate };
      } else {
        query.createdAt = { $gt: cursorDate };
      }
    }

    const total = await AccountNumber.countDocuments(query);

    // Build sort object
    const sortObj: any = {};
    sortObj[sort] = order === 'desc' ? -1 : 1;

    // Execute query
    const accounts = await AccountNumber.find(query)
      .populate('userId', 'firstName lastName email phone userIsVerified kyc_status')
      .sort(sortObj)
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum)
      .lean();

    const transformedAccounts = accounts.map((account: any) => ({
      accountNumber: account.accountNumber,
      accountName: `${account.userId.firstName} ${account.userId.lastName}`,
      accountType: account.accountType,
      currency: account.currency,
      isActive: account.isActive,
      isVerified: account.userId.userIsVerified,
      kycStatus: account.userId.kyc_status,
      balances: account.balance.map((b: any) => ({
        currency: b.currency,
        walletBalance: b.walletBalance,
        ledgerBalance: b.ledgerBalance,
      })),
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    }));

    const paginationMeta = buildPaginationMeta(
      pageNum,
      limitNum,
      total,
      transformedAccounts,
      'createdAt'
    );

    const response: PaginatedResponse<any> = {
      status: true,
      message: 'ACCOUNTS_RETRIEVED',
      data: transformedAccounts,
      meta: {
        pagination: paginationMeta,
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('Get accounts error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while retrieving accounts.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Create Credit Card
 * Creates a virtual credit card for a user account
 */
export const createCreditCard = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      accountNumber,
      cardType = 'VIRTUAL',
      currency = 'USD',
      spendingLimit,
      metadata
    } = req.body;

    if (!accountNumber) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Account number is required.',
          suggestions: ['Provide a valid account number.'],
        },
      });
    }

    const sanitizedAccountNumber = sanitizeInput(accountNumber);

    // Find the account
    const account = await AccountNumber.findOne({
      accountNumber: sanitizedAccountNumber
    }).populate('userId');

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account found with the provided account number.',
          suggestions: ['Verify the account number and try again.'],
        },
      });
    }

    // Import Stripe service
    const { StripeService } = require('../services/stripe.service');
    const stripeService = new StripeService();

    // Create cardholder and issue card
    const cardResult = await stripeService.createCardholderAndIssueCard({
      userId: account.userId._id,
      accountNumber: sanitizedAccountNumber,
      cardType,
      currency,
      spendingLimit,
      metadata: {
        createdBy: 'DEVELOPER_API',
        clientId: (req as any).clientId,
        ...metadata,
      }
    });

    return res.status(201).json({
      status: true,
      message: 'CREDIT_CARD_CREATED',
      data: {
        cardId: cardResult.cardId,
        accountNumber: sanitizedAccountNumber,
        cardType,
        currency,
        last4: cardResult.last4,
        expiryMonth: cardResult.expiryMonth,
        expiryYear: cardResult.expiryYear,
        spendingLimit,
        status: cardResult.status,
        createdAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Create credit card error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while creating the credit card.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Get Credit Cards
 * Retrieves credit cards for an account with pagination
 */
export const getCreditCards = async (req: Request, res: Response): Promise<any> => {
  try {
    const { accountNumber } = req.params;
    const {
      page = '1',
      limit = '20',
      status,
      cardType,
    } = req.query as {
      page?: string;
      limit?: string;
      status?: string;
      cardType?: string;
    };

    if (!accountNumber) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Account number is required.',
          suggestions: ['Provide a valid account number in the URL path.'],
        },
      });
    }

    const sanitizedAccountNumber = sanitizeInput(accountNumber);
    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 20));

    // Find the account
    const account = await AccountNumber.findOne({
      accountNumber: sanitizedAccountNumber
    });

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account found with the provided account number.',
          suggestions: ['Verify the account number and try again.'],
        },
      });
    }

    // Build query for credit cards
    const query: any = { userId: account.userId };

    if (status) query.status = sanitizeInput(status);
    if (cardType) query.cardType = sanitizeInput(cardType);

    // Import credit card model (assuming it exists)
    const CreditCard = require('../models/creditcard.model');

    const total = await CreditCard.countDocuments(query);
    const creditCards = await CreditCard.find(query)
      .sort({ createdAt: -1 })
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum)
      .lean();

    const transformedCards = creditCards.map((card: any) => ({
      cardId: card._id,
      accountNumber: sanitizedAccountNumber,
      cardType: card.cardType,
      currency: card.currency,
      last4: card.last4,
      expiryMonth: card.expiryMonth,
      expiryYear: card.expiryYear,
      status: card.status,
      spendingLimit: card.spendingLimit,
      currentBalance: card.currentBalance,
      createdAt: card.createdAt,
      updatedAt: card.updatedAt,
    }));

    const paginationMeta = buildPaginationMeta(
      pageNum,
      limitNum,
      total,
      transformedCards,
      'createdAt'
    );

    const response: PaginatedResponse<any> = {
      status: true,
      message: 'CREDIT_CARDS_RETRIEVED',
      data: transformedCards,
      meta: {
        pagination: paginationMeta,
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('Get credit cards error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while retrieving credit cards.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Fund Credit Card
 * Adds funds to a credit card
 */
export const fundCreditCard = async (req: Request, res: Response): Promise<any> => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { cardId } = req.params;
    const { amount, currency, reference, description } = req.body;

    if (!cardId || !amount || !currency || !reference) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Missing required fields: cardId, amount, currency, reference.',
          suggestions: ['Provide all required fields.'],
        },
      });
    }

    const sanitizedCardId = sanitizeInput(cardId);
    const sanitizedCurrency = sanitizeInput(currency);
    const sanitizedReference = sanitizeInput(reference);

    if (typeof amount !== 'number' || amount <= 0) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({
        status: false,
        message: 'INVALID_AMOUNT',
        meta: {
          error: 'Amount must be a positive number.',
          suggestions: ['Provide a valid amount greater than 0.'],
        },
      });
    }

    // Import credit card model
    const CreditCard = require('../models/creditcard.model');

    const creditCard = await CreditCard.findById(sanitizedCardId).session(session);
    if (!creditCard) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json({
        status: false,
        message: 'CREDIT_CARD_NOT_FOUND',
        meta: {
          error: 'No credit card found with the provided ID.',
          suggestions: ['Verify the card ID and try again.'],
        },
      });
    }

    // Update card balance
    creditCard.currentBalance += amount;
    await creditCard.save({ session });

    // Create transaction record
    const transaction = new Transaction({
      type: 'CARD_FUNDING',
      amount,
      currency: sanitizedCurrency,
      status: 'COMPLETED',
      reference: sanitizedReference,
      description: description || 'Credit card funding via API',
      recipient: {
        userId: creditCard.userId,
        cardId: sanitizedCardId,
        name: `Card ending in ${creditCard.last4}`,
      },
      metadata: {
        source: 'DEVELOPER_API',
        clientId: (req as any).clientId,
        cardType: creditCard.cardType,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await transaction.save({ session });

    await session.commitTransaction();
    session.endSession();

    // Trigger webhook
    await triggerWebhook('card.funded', {
      cardId: sanitizedCardId,
      amount,
      currency: sanitizedCurrency,
      reference: sanitizedReference,
      newBalance: creditCard.currentBalance,
      timestamp: new Date().toISOString(),
    }, (req as any).clientId);

    return res.status(200).json({
      status: true,
      message: 'CREDIT_CARD_FUNDED',
      data: {
        cardId: sanitizedCardId,
        amount,
        currency: sanitizedCurrency,
        reference: sanitizedReference,
        newBalance: creditCard.currentBalance,
        transactionId: transaction._id,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    console.error('Fund credit card error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while funding the credit card.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Update Credit Card
 * Updates credit card settings
 */
export const updateCreditCard = async (req: Request, res: Response): Promise<any> => {
  try {
    const { cardId } = req.params;
    const { spendingLimit, status, metadata } = req.body;

    if (!cardId) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Card ID is required.',
          suggestions: ['Provide a valid card ID in the URL path.'],
        },
      });
    }

    const sanitizedCardId = sanitizeInput(cardId);

    // Import credit card model
    const CreditCard = require('../models/creditcard.model');

    const creditCard = await CreditCard.findById(sanitizedCardId);
    if (!creditCard) {
      return res.status(404).json({
        status: false,
        message: 'CREDIT_CARD_NOT_FOUND',
        meta: {
          error: 'No credit card found with the provided ID.',
          suggestions: ['Verify the card ID and try again.'],
        },
      });
    }

    // Update fields
    const updates: any = { updatedAt: new Date() };

    if (spendingLimit !== undefined) {
      if (typeof spendingLimit !== 'number' || spendingLimit < 0) {
        return res.status(400).json({
          status: false,
          message: 'INVALID_SPENDING_LIMIT',
          meta: {
            error: 'Spending limit must be a non-negative number.',
            suggestions: ['Provide a valid spending limit.'],
          },
        });
      }
      updates.spendingLimit = spendingLimit;
    }

    if (status) {
      const validStatuses = ['ACTIVE', 'INACTIVE', 'BLOCKED', 'EXPIRED'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          status: false,
          message: 'INVALID_STATUS',
          meta: {
            error: `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
            suggestions: ['Provide a valid status.'],
          },
        });
      }
      updates.status = status;
    }

    if (metadata) {
      updates.metadata = { ...creditCard.metadata, ...metadata };
    }

    await CreditCard.findByIdAndUpdate(sanitizedCardId, updates);

    // Trigger webhook
    await triggerWebhook('card.updated', {
      cardId: sanitizedCardId,
      updates,
      timestamp: new Date().toISOString(),
    }, (req as any).clientId);

    return res.status(200).json({
      status: true,
      message: 'CREDIT_CARD_UPDATED',
      data: {
        cardId: sanitizedCardId,
        updates,
        timestamp: updates.updatedAt,
      },
    });
  } catch (error) {
    console.error('Update credit card error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while updating the credit card.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Create Webhook
 * Registers a webhook endpoint for event notifications
 */
export const createWebhook = async (req: Request, res: Response): Promise<any> => {
  try {
    const { url, events, description } = req.body;

    if (!url || !events || !Array.isArray(events)) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'URL and events array are required.',
          suggestions: ['Provide a valid URL and array of events to subscribe to.'],
        },
      });
    }

    const sanitizedUrl = sanitizeInput(url);

    // Validate URL format
    if (!validator.isURL(sanitizedUrl)) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_URL',
        meta: {
          error: 'Invalid URL format.',
          suggestions: ['Provide a valid HTTPS URL.'],
        },
      });
    }

    // Validate events
    const validEvents = [
      'account.created',
      'account.updated',
      'transaction.created',
      'transaction.completed',
      'transaction.failed',
      'card.created',
      'card.funded',
      'card.updated',
      'card.blocked',
      'funds.pushed',
      'balance.updated',
    ];

    const invalidEvents = events.filter((event: string) => !validEvents.includes(event));
    if (invalidEvents.length > 0) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_EVENTS',
        meta: {
          error: `Invalid events: ${invalidEvents.join(', ')}`,
          suggestions: [`Valid events: ${validEvents.join(', ')}`],
        },
      });
    }

    // Generate webhook secret
    const crypto = require('crypto');
    const secret = crypto.randomBytes(32).toString('hex');

    // Create webhook configuration
    const WebhookConfig = require('../models/webhook.model');
    const webhook = await WebhookConfig.create({
      clientId: (req as any).clientId,
      url: sanitizedUrl,
      events,
      description: description || '',
      secret,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return res.status(201).json({
      status: true,
      message: 'WEBHOOK_CREATED',
      data: {
        webhookId: webhook._id,
        url: sanitizedUrl,
        events,
        description: webhook.description,
        secret, // Return secret only once during creation
        isActive: true,
        createdAt: webhook.createdAt,
      },
    });
  } catch (error) {
    console.error('Create webhook error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while creating the webhook.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Get Webhooks
 * Retrieves webhook configurations for the client
 */
export const getWebhooks = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page = '1',
      limit = '20',
      isActive,
    } = req.query as {
      page?: string;
      limit?: string;
      isActive?: string;
    };

    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 20));

    const query: any = { clientId: (req as any).clientId };

    if (isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    const WebhookConfig = require('../models/webhook.model');

    const total = await WebhookConfig.countDocuments(query);
    const webhooks = await WebhookConfig.find(query)
      .sort({ createdAt: -1 })
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum)
      .select('-secret') // Don't return secrets in list
      .lean();

    const paginationMeta = buildPaginationMeta(
      pageNum,
      limitNum,
      total,
      webhooks,
      'createdAt'
    );

    const response: PaginatedResponse<any> = {
      status: true,
      message: 'WEBHOOKS_RETRIEVED',
      data: webhooks.map((webhook: any) => ({
        webhookId: webhook._id,
        url: webhook.url,
        events: webhook.events,
        description: webhook.description,
        isActive: webhook.isActive,
        createdAt: webhook.createdAt,
        updatedAt: webhook.updatedAt,
      })),
      meta: {
        pagination: paginationMeta,
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('Get webhooks error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while retrieving webhooks.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Update Webhook
 * Updates webhook configuration
 */
export const updateWebhook = async (req: Request, res: Response): Promise<any> => {
  try {
    const { webhookId } = req.params;
    const { url, events, description, isActive } = req.body;

    if (!webhookId) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Webhook ID is required.',
          suggestions: ['Provide a valid webhook ID in the URL path.'],
        },
      });
    }

    const sanitizedWebhookId = sanitizeInput(webhookId);

    const WebhookConfig = require('../models/webhook.model');
    const webhook = await WebhookConfig.findOne({
      _id: sanitizedWebhookId,
      clientId: (req as any).clientId,
    });

    if (!webhook) {
      return res.status(404).json({
        status: false,
        message: 'WEBHOOK_NOT_FOUND',
        meta: {
          error: 'No webhook found with the provided ID.',
          suggestions: ['Verify the webhook ID and try again.'],
        },
      });
    }

    const updates: any = { updatedAt: new Date() };

    if (url) {
      const sanitizedUrl = sanitizeInput(url);
      if (!validator.isURL(sanitizedUrl)) {
        return res.status(400).json({
          status: false,
          message: 'INVALID_URL',
          meta: {
            error: 'Invalid URL format.',
            suggestions: ['Provide a valid HTTPS URL.'],
          },
        });
      }
      updates.url = sanitizedUrl;
    }

    if (events && Array.isArray(events)) {
      const validEvents = [
        'account.created', 'account.updated', 'transaction.created',
        'transaction.completed', 'transaction.failed', 'card.created',
        'card.funded', 'card.updated', 'card.blocked', 'funds.pushed',
        'balance.updated',
      ];

      const invalidEvents = events.filter((event: string) => !validEvents.includes(event));
      if (invalidEvents.length > 0) {
        return res.status(400).json({
          status: false,
          message: 'INVALID_EVENTS',
          meta: {
            error: `Invalid events: ${invalidEvents.join(', ')}`,
            suggestions: [`Valid events: ${validEvents.join(', ')}`],
          },
        });
      }
      updates.events = events;
    }

    if (description !== undefined) {
      updates.description = sanitizeInput(description);
    }

    if (isActive !== undefined) {
      updates.isActive = Boolean(isActive);
    }

    await WebhookConfig.findByIdAndUpdate(sanitizedWebhookId, updates);

    return res.status(200).json({
      status: true,
      message: 'WEBHOOK_UPDATED',
      data: {
        webhookId: sanitizedWebhookId,
        updates,
        timestamp: updates.updatedAt,
      },
    });
  } catch (error) {
    console.error('Update webhook error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while updating the webhook.',
        suggestions: ['Try again later.'],
      },
    });
  }
};

/**
 * Developer API: Delete Webhook
 * Deletes a webhook configuration
 */
export const deleteWebhook = async (req: Request, res: Response): Promise<any> => {
  try {
    const { webhookId } = req.params;

    if (!webhookId) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_REQUEST',
        meta: {
          error: 'Webhook ID is required.',
          suggestions: ['Provide a valid webhook ID in the URL path.'],
        },
      });
    }

    const sanitizedWebhookId = sanitizeInput(webhookId);

    const WebhookConfig = require('../models/webhook.model');
    const result = await WebhookConfig.findOneAndDelete({
      _id: sanitizedWebhookId,
      clientId: (req as any).clientId,
    });

    if (!result) {
      return res.status(404).json({
        status: false,
        message: 'WEBHOOK_NOT_FOUND',
        meta: {
          error: 'No webhook found with the provided ID.',
          suggestions: ['Verify the webhook ID and try again.'],
        },
      });
    }

    return res.status(200).json({
      status: true,
      message: 'WEBHOOK_DELETED',
      data: {
        webhookId: sanitizedWebhookId,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Delete webhook error:', error);
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: 'An error occurred while deleting the webhook.',
        suggestions: ['Try again later.'],
      },
    });
  }
};
