# SangaPay Developer API Documentation

## Overview

The SangaPay Developer API provides OAuth 2.0 protected endpoints for third-party developers to integrate with the SangaPay platform. This API is separate from the main application routes and requires proper OAuth authentication and scope-based permissions.

## Base URL

```
https://your-domain.com/api/v1/developer
```

## Authentication

### OAuth 2.0 Flow

1. **Register your application** via `/api/v1/oauth/register`
2. **Get authorization** via `/api/v1/oauth/authorize`
3. **Exchange code for token** via `/api/v1/oauth/token`
4. **Use access token** in API requests

### Supported Grant Types

- `authorization_code` - For server-side applications
- `client_credentials` - For machine-to-machine communication
- `refresh_token` - For token refresh

### Required Headers

```http
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
```

## OAuth Scopes

| Scope | Description |
|-------|-------------|
| `read:profile` | Read user profile information |
| `read:accounts` | Read account information and validate accounts |
| `read:balance` | Read account balances |
| `read:transactions` | Read transaction history |
| `write:transfers` | Initiate transfers and fund operations |
| `offline_access` | Long-term access via refresh tokens |

## Endpoints

### Account Validation

#### Validate Account
```http
GET /accounts/validate/{accountNumber}
```
**Scope:** `read:accounts`

**Response:**
```json
{
  "status": true,
  "message": "ACCOUNT_VALIDATED",
  "data": {
    "accountNumber": "**********",
    "accountName": "John Doe",
    "accountType": "PERSONAL",
    "currency": "USD",
    "isActive": true,
    "isVerified": true,
    "kycStatus": "approved",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Get Account Balance
```http
GET /accounts/{accountNumber}/balance?currencies=USD,LRD
```
**Scope:** `read:balance`

**Query Parameters:**
- `currencies` (optional): Comma-separated list of currencies

**Response:**
```json
{
  "status": true,
  "message": "BALANCE_RETRIEVED",
  "data": {
    "accountNumber": "**********",
    "accountName": "John Doe",
    "balances": [
      {
        "currency": "USD",
        "walletBalance": 1000.50,
        "ledgerBalance": 1000.50,
        "lastUpdated": "2024-01-01T00:00:00.000Z"
      }
    ],
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Fund Management

#### Push Funds
```http
POST /funds/push
```
**Scope:** `write:transfers`

**Request Body:**
```json
{
  "accountNumber": "**********",
  "amount": 100.00,
  "currency": "USD",
  "reference": "TXN-12345",
  "description": "API fund credit",
  "metadata": {
    "source": "external_system",
    "batch_id": "BATCH-001"
  }
}
```

**Response:**
```json
{
  "status": true,
  "message": "FUNDS_CREDITED",
  "data": {
    "transactionId": "507f1f77bcf86cd799439011",
    "accountNumber": "**********",
    "amount": 100.00,
    "currency": "USD",
    "reference": "TXN-12345",
    "newBalance": 1100.50,
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Transaction History

#### Get Transactions
```http
GET /accounts/{accountNumber}/transactions
```
**Scope:** `read:transactions`

**Query Parameters:**
- `page` (default: 1): Page number
- `limit` (default: 20, max: 100): Items per page
- `cursor`: Cursor for cursor-based pagination
- `sort` (default: createdAt): Field to sort by
- `order` (default: desc): Sort order (asc/desc)
- `status`: Filter by transaction status
- `type`: Filter by transaction type
- `currency`: Filter by currency
- `startDate`: Start date (ISO format)
- `endDate`: End date (ISO format)

**Response:**
```json
{
  "status": true,
  "message": "TRANSACTIONS_RETRIEVED",
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "type": "DEPOSIT",
      "amount": 100.00,
      "currency": "USD",
      "status": "COMPLETED",
      "reference": "TXN-12345",
      "description": "API fund credit",
      "direction": "CREDIT",
      "counterparty": {
        "accountNumber": "**********",
        "name": "Jane Smith"
      },
      "metadata": {},
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false,
      "nextCursor": "2024-01-01T00:00:00.000Z",
      "prevCursor": null
    }
  }
}
```

### Reconciliation

#### Get Reconciliation Report
```http
GET /reconciliation/report?startDate=2024-01-01&endDate=2024-01-31
```
**Scope:** `read:transactions`, `read:accounts`

**Query Parameters:**
- `startDate` (required): Start date (YYYY-MM-DD)
- `endDate` (required): End date (YYYY-MM-DD)
- `currency` (optional): Filter by currency
- `page` (default: 1): Page number
- `limit` (default: 50, max: 100): Items per page

**Response:**
```json
{
  "status": true,
  "message": "RECONCILIATION_REPORT_GENERATED",
  "data": {
    "period": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-01-31T23:59:59.999Z"
    },
    "summary": {
      "currencies": [
        {
          "_id": "USD",
          "types": [
            {
              "type": "DEPOSIT",
              "totalAmount": 10000.00,
              "count": 50
            }
          ],
          "totalTransactions": 100,
          "totalVolume": 50000.00
        }
      ],
      "currentBalances": {
        "USD": {
          "totalWalletBalance": 100000.00,
          "totalLedgerBalance": 100000.00,
          "accountCount": 25
        }
      }
    },
    "transactions": [],
    "generatedAt": "2024-01-01T00:00:00.000Z",
    "generatedBy": "your_client_id"
  },
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 100,
      "totalPages": 2,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### Account Management

#### Get Accounts List
```http
GET /accounts
```
**Scope:** `read:accounts`

**Query Parameters:**
- `page`, `limit`, `cursor`, `sort`, `order`: Pagination parameters
- `currency`: Filter by currency
- `isActive`: Filter by active status (true/false)
- `search`: Search by account number, name, or email

## Pagination

All list endpoints support both offset-based and cursor-based pagination:

### Offset-based Pagination
```http
GET /accounts?page=2&limit=20
```

### Cursor-based Pagination
```http
GET /accounts?cursor=2024-01-01T00:00:00.000Z&limit=20
```

### Pagination Response
```json
{
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false,
      "nextCursor": "2024-01-01T00:00:00.000Z",
      "prevCursor": null
    }
  }
}
```

## Rate Limits

- **Default:** 100 requests per 15 minutes
- **Burst:** 10 requests per second

## Error Responses

```json
{
  "status": false,
  "message": "ERROR_CODE",
  "meta": {
    "error": "Detailed error message",
    "suggestions": ["Helpful suggestion"]
  }
}
```

### Common Error Codes

- `400` - Bad Request: Invalid parameters
- `401` - Unauthorized: Invalid or missing OAuth token
- `403` - Forbidden: Insufficient permissions/scopes
- `404` - Not Found: Resource not found
- `429` - Too Many Requests: Rate limit exceeded
- `500` - Internal Server Error: Server error

## Getting Started

1. **Register your application:**
```bash
curl -X POST https://your-domain.com/api/v1/oauth/register \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "your_user_id",
    "client_name": "My App",
    "client_type": "confidential",
    "redirect_uris": ["https://myapp.com/callback"]
  }'
```

2. **Get authorization code:**
```
https://your-domain.com/api/v1/oauth/authorize?response_type=code&client_id=YOUR_CLIENT_ID&redirect_uri=YOUR_REDIRECT_URI&scope=read:accounts read:balance&state=random_state
```

3. **Exchange code for token:**
```bash
curl -X POST https://your-domain.com/api/v1/oauth/token \
  -H "Content-Type: application/json" \
  -d '{
    "grant_type": "authorization_code",
    "code": "AUTHORIZATION_CODE",
    "redirect_uri": "YOUR_REDIRECT_URI",
    "client_id": "YOUR_CLIENT_ID",
    "client_secret": "YOUR_CLIENT_SECRET"
  }'
```

4. **Use the API:**
```bash
curl -X GET https://your-domain.com/api/v1/developer/accounts/validate/********** \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Support

For API support, please contact our developer support team or refer to the interactive documentation at `/api/v1/developer/docs`.
