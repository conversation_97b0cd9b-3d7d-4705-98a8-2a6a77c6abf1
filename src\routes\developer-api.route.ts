import express, { Request, Response, NextFunction } from 'express';
import {
  validateAccount,
  pushFunds,
  getAccountBalance,
  getTransactions,
  getReconciliationReport,
  getAccounts,
  createCreditCard,
  getCreditCards,
  fundCreditCard,
  updateCreditCard,
  createWebhook,
  getWebhooks,
  updateWebhook,
  deleteWebhook,
} from '../controllers/developer-api.controller';
import { authenticateOAuth, checkPermission } from '../middleware/auth.middleware';
import { validateRequestPayload } from '../middleware/security.middleware';
import { enhancedSecurityForSensitiveRoutes } from '../middleware/data-protection.middleware';
import { apiLimiter } from '../middleware/rate-limit.middleware';

// Helper function to wrap async route handlers
const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

const developerApiRouter = express.Router();

// Apply enhanced security and rate limiting to all developer API endpoints
developerApiRouter.use(enhancedSecurityForSensitiveRoutes);
developerApiRouter.use(apiLimiter);

// All developer API routes require OAuth authentication
developerApiRouter.use(authenticateOAuth);

/**
 * Account Validation Endpoints
 */

// GET /api/v1/developer/accounts/validate/:accountNumber
// Scope: read:accounts
developerApiRouter.get(
  '/accounts/validate/:accountNumber',
  checkPermission(['read:accounts']),
  asyncHandler(validateAccount)
);

// GET /api/v1/developer/accounts/:accountNumber/balance
// Scope: read:balance
developerApiRouter.get(
  '/accounts/:accountNumber/balance',
  checkPermission(['read:balance']),
  asyncHandler(getAccountBalance)
);

/**
 * Transaction Endpoints
 */

// GET /api/v1/developer/accounts/:accountNumber/transactions
// Scope: read:transactions
developerApiRouter.get(
  '/accounts/:accountNumber/transactions',
  checkPermission(['read:transactions']),
  asyncHandler(getTransactions)
);

/**
 * Fund Management Endpoints
 */

// POST /api/v1/developer/funds/push
// Scope: write:transfers
developerApiRouter.post(
  '/funds/push',
  validateRequestPayload,
  checkPermission(['write:transfers']),
  asyncHandler(pushFunds)
);

/**
 * Reconciliation Endpoints
 */

// GET /api/v1/developer/reconciliation/report
// Scope: read:transactions, read:accounts
developerApiRouter.get(
  '/reconciliation/report',
  checkPermission(['read:transactions', 'read:accounts']),
  asyncHandler(getReconciliationReport)
);

/**
 * Account Management Endpoints
 */

// GET /api/v1/developer/accounts
// Scope: read:accounts
developerApiRouter.get(
  '/accounts',
  checkPermission(['read:accounts']),
  asyncHandler(getAccounts)
);

/**
 * Credit Card Management Endpoints
 */

// POST /api/v1/developer/cards/create
// Scope: write:transfers
developerApiRouter.post(
  '/cards/create',
  validateRequestPayload,
  checkPermission(['write:transfers']),
  asyncHandler(createCreditCard)
);

// GET /api/v1/developer/accounts/:accountNumber/cards
// Scope: read:accounts
developerApiRouter.get(
  '/accounts/:accountNumber/cards',
  checkPermission(['read:accounts']),
  asyncHandler(getCreditCards)
);

// POST /api/v1/developer/cards/:cardId/fund
// Scope: write:transfers
developerApiRouter.post(
  '/cards/:cardId/fund',
  validateRequestPayload,
  checkPermission(['write:transfers']),
  asyncHandler(fundCreditCard)
);

// PUT /api/v1/developer/cards/:cardId
// Scope: write:transfers
developerApiRouter.put(
  '/cards/:cardId',
  validateRequestPayload,
  checkPermission(['write:transfers']),
  asyncHandler(updateCreditCard)
);

/**
 * Webhook Management Endpoints
 */

// POST /api/v1/developer/webhooks
// Scope: read:profile (basic scope for webhook management)
developerApiRouter.post(
  '/webhooks',
  validateRequestPayload,
  checkPermission(['read:profile']),
  asyncHandler(createWebhook)
);

// GET /api/v1/developer/webhooks
// Scope: read:profile
developerApiRouter.get(
  '/webhooks',
  checkPermission(['read:profile']),
  asyncHandler(getWebhooks)
);

// PUT /api/v1/developer/webhooks/:webhookId
// Scope: read:profile
developerApiRouter.put(
  '/webhooks/:webhookId',
  validateRequestPayload,
  checkPermission(['read:profile']),
  asyncHandler(updateWebhook)
);

// DELETE /api/v1/developer/webhooks/:webhookId
// Scope: read:profile
developerApiRouter.delete(
  '/webhooks/:webhookId',
  checkPermission(['read:profile']),
  asyncHandler(deleteWebhook)
);

/**
 * API Documentation Endpoint
 */
developerApiRouter.get('/docs', (req: Request, res: Response) => {
  res.json({
    status: true,
    message: 'DEVELOPER_API_DOCUMENTATION',
    data: {
      version: '1.0.0',
      baseUrl: '/api/v1/developer',
      authentication: {
        type: 'OAuth 2.0',
        flows: ['authorization_code', 'client_credentials'],
        scopes: {
          'read:profile': 'Read user profile information',
          'read:accounts': 'Read account information and validate accounts',
          'read:balance': 'Read account balances',
          'read:transactions': 'Read transaction history',
          'write:transfers': 'Initiate transfers and fund operations',
          'offline_access': 'Long-term access via refresh tokens',
        },
      },
      endpoints: {
        accounts: {
          validate: {
            method: 'GET',
            path: '/accounts/validate/{accountNumber}',
            scope: 'read:accounts',
            description: 'Validate if an account exists and get basic details',
          },
          balance: {
            method: 'GET',
            path: '/accounts/{accountNumber}/balance',
            scope: 'read:balance',
            description: 'Get account balance for specific currencies',
            queryParams: ['currencies'],
          },
          list: {
            method: 'GET',
            path: '/accounts',
            scope: 'read:accounts',
            description: 'Get paginated list of accounts',
            queryParams: ['page', 'limit', 'cursor', 'sort', 'order', 'currency', 'isActive', 'search'],
          },
          transactions: {
            method: 'GET',
            path: '/accounts/{accountNumber}/transactions',
            scope: 'read:transactions',
            description: 'Get paginated transaction history for an account',
            queryParams: ['page', 'limit', 'cursor', 'sort', 'order', 'status', 'type', 'currency', 'startDate', 'endDate'],
          },
        },
        cards: {
          create: {
            method: 'POST',
            path: '/cards/create',
            scope: 'write:transfers',
            description: 'Create a virtual credit card for an account',
            bodyParams: ['accountNumber', 'cardType', 'currency', 'spendingLimit', 'metadata'],
          },
          list: {
            method: 'GET',
            path: '/accounts/{accountNumber}/cards',
            scope: 'read:accounts',
            description: 'Get credit cards for an account',
            queryParams: ['page', 'limit', 'status', 'cardType'],
          },
          fund: {
            method: 'POST',
            path: '/cards/{cardId}/fund',
            scope: 'write:transfers',
            description: 'Add funds to a credit card',
            bodyParams: ['amount', 'currency', 'reference', 'description'],
          },
          update: {
            method: 'PUT',
            path: '/cards/{cardId}',
            scope: 'write:transfers',
            description: 'Update credit card settings',
            bodyParams: ['spendingLimit', 'status', 'metadata'],
          },
        },
        funds: {
          push: {
            method: 'POST',
            path: '/funds/push',
            scope: 'write:transfers',
            description: 'Credit funds to a user account',
            bodyParams: ['accountNumber', 'amount', 'currency', 'reference', 'description', 'metadata'],
          },
        },
        reconciliation: {
          report: {
            method: 'GET',
            path: '/reconciliation/report',
            scope: 'read:transactions,read:accounts',
            description: 'Generate reconciliation report for a date range',
            queryParams: ['startDate', 'endDate', 'currency', 'page', 'limit'],
          },
        },
        webhooks: {
          create: {
            method: 'POST',
            path: '/webhooks',
            scope: 'read:profile',
            description: 'Create a webhook endpoint for event notifications',
            bodyParams: ['url', 'events', 'description'],
          },
          list: {
            method: 'GET',
            path: '/webhooks',
            scope: 'read:profile',
            description: 'Get webhook configurations',
            queryParams: ['page', 'limit', 'isActive'],
          },
          update: {
            method: 'PUT',
            path: '/webhooks/{webhookId}',
            scope: 'read:profile',
            description: 'Update webhook configuration',
            bodyParams: ['url', 'events', 'description', 'isActive'],
          },
          delete: {
            method: 'DELETE',
            path: '/webhooks/{webhookId}',
            scope: 'read:profile',
            description: 'Delete a webhook configuration',
          },
        },
      },
      pagination: {
        description: 'All list endpoints support pagination',
        parameters: {
          page: 'Page number (default: 1)',
          limit: 'Items per page (default: 20, max: 100)',
          cursor: 'Cursor for cursor-based pagination',
          sort: 'Field to sort by (default: createdAt)',
          order: 'Sort order: asc or desc (default: desc)',
        },
        response: {
          meta: {
            pagination: {
              page: 'Current page number',
              limit: 'Items per page',
              total: 'Total number of items',
              totalPages: 'Total number of pages',
              hasNext: 'Whether there are more pages',
              hasPrev: 'Whether there are previous pages',
              nextCursor: 'Cursor for next page (if available)',
              prevCursor: 'Cursor for previous page (if available)',
            },
          },
        },
      },
      rateLimits: {
        default: '100 requests per 15 minutes',
        burst: '10 requests per second',
      },
      errors: {
        400: 'Bad Request - Invalid parameters',
        401: 'Unauthorized - Invalid or missing OAuth token',
        403: 'Forbidden - Insufficient permissions/scopes',
        404: 'Not Found - Resource not found',
        429: 'Too Many Requests - Rate limit exceeded',
        500: 'Internal Server Error - Server error',
      },
    },
  });
});

/**
 * Health Check Endpoint (no authentication required)
 */
developerApiRouter.get('/health', (_req: Request, res: Response) => {
  res.json({
    status: true,
    message: 'DEVELOPER_API_HEALTHY',
    data: {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
    },
  });
});

export default developerApiRouter;
