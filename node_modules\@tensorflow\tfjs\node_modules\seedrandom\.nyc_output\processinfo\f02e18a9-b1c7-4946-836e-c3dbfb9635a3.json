{"uuid": "f02e18a9-b1c7-4946-836e-c3dbfb9635a3", "parent": null, "pid": 8128, "argv": ["/home/<USER>/.nvm/versions/node/v11.9.0/bin/node", "/home/<USER>/git/seedrandom/node_modules/mocha/bin/_mocha", "test/cryptotest.js", "test/nodetest.js", "test/prngtest.js"], "execArgv": [], "cwd": "/home/<USER>/git/seedrandom", "time": 1568716532937, "ppid": 8121, "root": "23c2e786-6463-4b5a-9042-75ea3257640f", "coverageFilename": "/home/<USER>/git/seedrandom/.nyc_output/f02e18a9-b1c7-4946-836e-c3dbfb9635a3.json", "files": ["/home/<USER>/git/seedrandom/seedrandom.js", "/home/<USER>/git/seedrandom/lib/xor128.js", "/home/<USER>/git/seedrandom/lib/xorwow.js", "/home/<USER>/git/seedrandom/lib/xorshift7.js", "/home/<USER>/git/seedrandom/lib/xor4096.js", "/home/<USER>/git/seedrandom/lib/tychei.js", "/home/<USER>/git/seedrandom/lib/alea.js"]}