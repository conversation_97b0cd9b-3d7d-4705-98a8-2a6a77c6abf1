use actix_web::{web, HttpResponse, Responder};
use serde_json::json;

// Import route modules
pub mod account;
pub mod health;
pub mod user;
pub mod auth;
pub mod oauth;
pub mod developer_api;

/// Configure all routes
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    // Main API routes (for normal users - direct access)
    cfg.service(
        web::scope("/api/v1")
            .configure(account::configure_routes)
            .configure(user::configure_routes)
            .configure(auth::configure_routes)
    );

    // OAuth 2.0 routes
    cfg.service(
        web::scope("/api/v1/oauth")
            .configure(oauth::configure_routes)
    );

    // Developer API routes (OAuth-protected)
    cfg.service(
        web::scope("/api/v1")
            .service(developer_api::configure_developer_api())
    );

    // Health check routes
    cfg.service(
        web::scope("/api/health")
            .configure(health::configure_routes)
    );

    cfg.service(
        web::scope("/api/v1/health")
            .configure(health::configure_routes)
    );
}

/// Handler for 404 Not Found
pub async fn not_found() -> impl Responder {
    HttpResponse::NotFound().json(json!({
        "status": false,
        "message": "NOT_FOUND",
        "meta": {
            "error": "The requested resource was not found.",
            "suggestions": ["Check the URL and try again."]
        }
    }))
}
