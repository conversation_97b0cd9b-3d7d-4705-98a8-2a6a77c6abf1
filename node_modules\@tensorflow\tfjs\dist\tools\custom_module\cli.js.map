{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../../../tools/custom_module/cli.ts"], "names": [], "mappings": ";;AAEA;;;;;;;;;;;;;;;GAeG;;AAEH;;GAEG;AACH,uBAAyB;AACzB,2BAA6B;AAC7B,6BAA+B;AAE/B,mDAAsD;AAEtD,+BAA4B;AAC5B,iCAAkF;AAClF,6DAAwD;AAExD,mDAAmD;AACnD,IAAI,cAA8B,CAAC;AAEnC,IAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;AAEzD,IAAM,0BAA0B,GAAoC;IAClE,OAAO,EAAE,EAAE;IACX,MAAM,EAAE,EAAE;IACV,OAAO,EAAE,EAAE;IACX,eAAe,EAAE,IAAI;IACrB,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;IAC1B,aAAa,EAAE,EAAE;CAClB,CAAC;AAEF,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;IAC9B,MAAM,EAAE;QACN,WAAW,EAAE,oCAAoC;QACjD,IAAI,EAAE,QAAQ;QACd,YAAY,EAAE,IAAI;KACnB;CACF,CAAC,CAAC;AAEH,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;AAI5B,SAAS,YAAY;IACnB,IAAI,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;IACjC,IAAI,cAAc,IAAI,IAAI,EAAE;QAC1B,IAAA,WAAI,EAAC,8BAA8B,CAAC,CAAC;KACtC;IAED,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IAEzD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;QAClC,IAAA,WAAI,EAAC,+CAAwC,cAAc,CAAE,CAAC,CAAC;KAChE;IACD,IAAI,MAAM,CAAC;IACX,IAAI;QACF,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;KAC/D;IAAC,OAAO,KAAK,EAAE;QACd,IAAA,WAAI,EAAC,0DAAmD,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC;KAC1E;IAED,IAAI,MAAM,CAAC,UAAU,IAAI,IAAI,EAAE;QAC7B,IAAA,WAAI,EAAC,kDAAkD,CAAC,CAAC;KAC1D;IAED,OAAO,CAAC,GAAG,CAAC,iDAA0C,cAAc,MAAG,CAAC,CAAC;IAEzE,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,0BAA0B,EAAE,MAAM,CAAC,CAAC;IAE1E,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACpC,IAAA,WAAI,EAAC,yCAAyC,CAAC,CAAC;KACjD;IAED,yCAAyC;IACzC,4CAA4C;IAC5C,kDAAkD;IAClD,IAAI;IAEJ,KAA+B,UAAoB,EAApB,KAAA,WAAW,CAAC,QAAQ,EAApB,cAAoB,EAApB,IAAoB,EAAE;QAAhD,IAAM,gBAAgB,SAAA;QACzB,IAAI,gBAAgB,KAAK,yBAAiB,CAAC,GAAG;YAC1C,gBAAgB,KAAK,yBAAiB,CAAC,KAAK;YAC5C,gBAAgB,KAAK,yBAAiB,CAAC,IAAI,EAAE;YAC/C,IAAA,WAAI,EAAC,gDAAyC,gBAAgB,MAAG,CAAC,CAAC;SACpE;KACF;IAED,yCAAyC;IACzC,SAAS,aAAa,CAAC,CAAS;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC3D,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC7D,WAAW,CAAC,oBAAoB,GAAG,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAEzE,cAAc,GAAG,IAAA,uCAAiB,EAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IAE9D,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;IAEhD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,uBAAuB,CAAC,MAA8B;IAC7D,oEAAoE;IACpE,wEAAwE;IACxE,4EAA4E;IAC5E,yDAAyD;IAEzD,0EAA0E;IAC1E,2EAA2E;IAC3E,yCAAyC;IACzC,SAAS,aAAa,CAAC,UAAkB;QACvC,0DAA0D;QAC1D,0CAA0C;QAC1C,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,2BAAe,CAAC,CAAC;IAC/C,CAAC;IAED,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAC9C,CAAC;AAED,IAAM,YAAY,GAAG,YAAY,EAAE,CAAC;AACpC,IAAM,gBAAgB,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;AAC/D,YAAY,CAAC,OAAO,GAAG,gBAAgB,CAAC;AACxC,IAAI,cAAc,IAAI,IAAI,EAAE;IAC1B,cAAc,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;CACtD;KAAM;IACL,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;CAC7D"}