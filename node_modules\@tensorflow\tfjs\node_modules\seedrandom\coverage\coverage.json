{"/home/<USER>/git/seedrandom/seedrandom.js": {"path": "/home/<USER>/git/seedrandom/seedrandom.js", "s": {"1": 6, "2": 6, "3": 1, "4": 23, "5": 23, "6": 23, "7": 23, "8": 23, "9": 3694, "10": 3694, "11": 3918, "12": 3918, "13": 3918, "14": 3694, "15": 9252, "16": 9252, "17": 9252, "18": 3694, "19": 23, "20": 1025, "21": 23, "22": 4100001, "23": 23, "24": 23, "25": 23, "26": 20, "27": 4, "28": 2, "29": 4, "30": 2, "31": 20, "32": 1, "33": 1, "34": 19, "35": 1, "36": 23, "37": 23, "38": 1, "39": 23, "40": 5888, "41": 23, "42": 5888, "43": 5888, "44": 23, "45": 4108661, "46": 4108661, "47": 16436074, "48": 16436074, "49": 4108661, "50": 4108661, "51": 4108661, "52": 1, "53": 4, "54": 4, "55": 4, "56": 4, "57": 1, "58": 174, "59": 174, "60": 8, "61": 151, "62": 151, "63": 174, "64": 1, "65": 52, "66": 52, "67": 46040, "68": 52, "69": 1, "70": 4, "71": 4, "72": 4, "73": 3, "74": 1, "75": 1, "76": 3, "77": 1, "78": 1, "79": 1, "80": 81, "81": 6, "82": 6, "83": 6, "84": 6, "85": 6, "86": 0, "87": 0, "88": 0, "89": 0}, "b": {"1": [1, 22], "2": [22, 11], "3": [2, 21], "4": [4, 17], "5": [23, 21, 20], "6": [4, 16], "7": [2, 2], "8": [1, 19], "9": [4, 19], "10": [1, 22], "11": [8, 166], "12": [174, 56], "13": [8, 166], "14": [29, 137], "15": [3, 1], "16": [4, 3], "17": [1, 1], "18": [1, 0], "19": [6, 0], "20": [6, 6], "21": [0, 0], "22": [0, 0]}, "f": {"1": 6, "2": 23, "3": 3694, "4": 1025, "5": 4100001, "6": 20, "7": 2, "8": 23, "9": 4108661, "10": 4, "11": 174, "12": 52, "13": 4, "14": 81, "15": 0}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 25, "loc": {"start": {"line": 25, "column": 1}, "end": {"line": 25, "column": 23}}}, "2": {"name": "seedrandom", "line": 47, "loc": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 45}}}, "3": {"name": "(anonymous_3)", "line": 61, "loc": {"start": {"line": 61, "column": 13}, "end": {"line": 61, "column": 24}}}, "4": {"name": "(anonymous_4)", "line": 78, "loc": {"start": {"line": 78, "column": 15}, "end": {"line": 78, "column": 26}}}, "5": {"name": "(anonymous_5)", "line": 79, "loc": {"start": {"line": 79, "column": 15}, "end": {"line": 79, "column": 26}}}, "6": {"name": "(anonymous_6)", "line": 87, "loc": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 48}}}, "7": {"name": "(anonymous_7)", "line": 92, "loc": {"start": {"line": 92, "column": 23}, "end": {"line": 92, "column": 34}}}, "8": {"name": "ARC4", "line": 119, "loc": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 19}}}, "9": {"name": "(anonymous_9)", "line": 136, "loc": {"start": {"line": 136, "column": 10}, "end": {"line": 136, "column": 26}}}, "10": {"name": "copy", "line": 156, "loc": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 20}}}, "11": {"name": "flatten", "line": 167, "loc": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 29}}}, "12": {"name": "mixkey", "line": 182, "loc": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 27}}}, "13": {"name": "autoseed", "line": 196, "loc": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 20}}}, "14": {"name": "tostring", "line": 218, "loc": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 21}}}, "15": {"name": "(anonymous_15)", "line": 242, "loc": {"start": {"line": 242, "column": 9}, "end": {"line": 242, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 25, "column": 0}, "end": {"line": 253, "column": 2}}, "2": {"start": {"line": 32, "column": 0}, "end": {"line": 41, "column": 15}}, "3": {"start": {"line": 47, "column": 0}, "end": {"line": 107, "column": 1}}, "4": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 15}}, "5": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 68}}, "6": {"start": {"line": 52, "column": 2}, "end": {"line": 54, "column": 49}}, "7": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 27}}, "8": {"start": {"line": 61, "column": 2}, "end": {"line": 76, "column": 4}}, "9": {"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": 14}}, "10": {"start": {"line": 65, "column": 4}, "end": {"line": 69, "column": 5}}, "11": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 26}}, "12": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 17}}, "13": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 20}}, "14": {"start": {"line": 70, "column": 4}, "end": {"line": 74, "column": 5}}, "15": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 13}}, "16": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 13}}, "17": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 15}}, "18": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 23}}, "19": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 51}}, "20": {"start": {"line": 78, "column": 28}, "end": {"line": 78, "column": 49}}, "21": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 61}}, "22": {"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 59}}, "23": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 21}}, "24": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 33}}, "25": {"start": {"line": 86, "column": 2}, "end": {"line": 106, "column": 17}}, "26": {"start": {"line": 88, "column": 8}, "end": {"line": 93, "column": 9}}, "27": {"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 45}}, "28": {"start": {"line": 90, "column": 25}, "end": {"line": 90, "column": 43}}, "29": {"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": 60}}, "30": {"start": {"line": 92, "column": 36}, "end": {"line": 92, "column": 58}}, "31": {"start": {"line": 97, "column": 8}, "end": {"line": 101, "column": 25}}, "32": {"start": {"line": 97, "column": 28}, "end": {"line": 97, "column": 49}}, "33": {"start": {"line": 97, "column": 50}, "end": {"line": 97, "column": 62}}, "34": {"start": {"line": 101, "column": 13}, "end": {"line": 101, "column": 25}}, "35": {"start": {"line": 119, "column": 0}, "end": {"line": 150, "column": 1}}, "36": {"start": {"line": 120, "column": 2}, "end": {"line": 121, "column": 59}}, "37": {"start": {"line": 124, "column": 2}, "end": {"line": 124, "column": 36}}, "38": {"start": {"line": 124, "column": 17}, "end": {"line": 124, "column": 34}}, "39": {"start": {"line": 127, "column": 2}, "end": {"line": 129, "column": 3}}, "40": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 15}}, "41": {"start": {"line": 130, "column": 2}, "end": {"line": 133, "column": 3}}, "42": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 60}}, "43": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 13}}, "44": {"start": {"line": 136, "column": 2}, "end": {"line": 149, "column": 12}}, "45": {"start": {"line": 138, "column": 4}, "end": {"line": 139, "column": 37}}, "46": {"start": {"line": 140, "column": 4}, "end": {"line": 143, "column": 5}}, "47": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 32}}, "48": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 78}}, "49": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 13}}, "50": {"start": {"line": 144, "column": 14}, "end": {"line": 144, "column": 23}}, "51": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 13}}, "52": {"start": {"line": 156, "column": 0}, "end": {"line": 161, "column": 1}}, "53": {"start": {"line": 157, "column": 2}, "end": {"line": 157, "column": 12}}, "54": {"start": {"line": 158, "column": 2}, "end": {"line": 158, "column": 12}}, "55": {"start": {"line": 159, "column": 2}, "end": {"line": 159, "column": 20}}, "56": {"start": {"line": 160, "column": 2}, "end": {"line": 160, "column": 11}}, "57": {"start": {"line": 167, "column": 0}, "end": {"line": 175, "column": 1}}, "58": {"start": {"line": 168, "column": 2}, "end": {"line": 168, "column": 44}}, "59": {"start": {"line": 169, "column": 2}, "end": {"line": 173, "column": 3}}, "60": {"start": {"line": 170, "column": 4}, "end": {"line": 172, "column": 5}}, "61": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 70}}, "62": {"start": {"line": 171, "column": 12}, "end": {"line": 171, "column": 55}}, "63": {"start": {"line": 174, "column": 2}, "end": {"line": 174, "column": 71}}, "64": {"start": {"line": 182, "column": 0}, "end": {"line": 189, "column": 1}}, "65": {"start": {"line": 183, "column": 2}, "end": {"line": 183, "column": 43}}, "66": {"start": {"line": 184, "column": 2}, "end": {"line": 187, "column": 3}}, "67": {"start": {"line": 185, "column": 4}, "end": {"line": 186, "column": 74}}, "68": {"start": {"line": 188, "column": 2}, "end": {"line": 188, "column": 23}}, "69": {"start": {"line": 196, "column": 0}, "end": {"line": 212, "column": 1}}, "70": {"start": {"line": 197, "column": 2}, "end": {"line": 211, "column": 3}}, "71": {"start": {"line": 198, "column": 4}, "end": {"line": 198, "column": 12}}, "72": {"start": {"line": 199, "column": 4}, "end": {"line": 205, "column": 5}}, "73": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": 23}}, "74": {"start": {"line": 203, "column": 6}, "end": {"line": 203, "column": 34}}, "75": {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": 62}}, "76": {"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": 25}}, "77": {"start": {"line": 208, "column": 4}, "end": {"line": 209, "column": 45}}, "78": {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 71}}, "79": {"start": {"line": 218, "column": 0}, "end": {"line": 220, "column": 1}}, "80": {"start": {"line": 219, "column": 2}, "end": {"line": 219, "column": 41}}, "81": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 28}}, "82": {"start": {"line": 235, "column": 0}, "end": {"line": 246, "column": 1}}, "83": {"start": {"line": 236, "column": 2}, "end": {"line": 236, "column": 30}}, "84": {"start": {"line": 238, "column": 2}, "end": {"line": 240, "column": 17}}, "85": {"start": {"line": 239, "column": 4}, "end": {"line": 239, "column": 35}}, "86": {"start": {"line": 241, "column": 7}, "end": {"line": 246, "column": 1}}, "87": {"start": {"line": 242, "column": 2}, "end": {"line": 242, "column": 44}}, "88": {"start": {"line": 242, "column": 22}, "end": {"line": 242, "column": 40}}, "89": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 38}}}, "branchMap": {"1": {"line": 49, "type": "cond-expr", "locations": [{"start": {"line": 49, "column": 32}, "end": {"line": 49, "column": 49}}, {"start": {"line": 49, "column": 53}, "end": {"line": 49, "column": 66}}]}, "2": {"line": 49, "type": "binary-expr", "locations": [{"start": {"line": 49, "column": 53}, "end": {"line": 49, "column": 60}}, {"start": {"line": 49, "column": 64}, "end": {"line": 49, "column": 66}}]}, "3": {"line": 53, "type": "cond-expr", "locations": [{"start": {"line": 53, "column": 22}, "end": {"line": 53, "column": 44}}, {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 38}}]}, "4": {"line": 54, "type": "cond-expr", "locations": [{"start": {"line": 54, "column": 21}, "end": {"line": 54, "column": 31}}, {"start": {"line": 54, "column": 34}, "end": {"line": 54, "column": 38}}]}, "5": {"line": 86, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 22}}, {"start": {"line": 86, "column": 26}, "end": {"line": 86, "column": 34}}, {"start": {"line": 87, "column": 6}, "end": {"line": 102, "column": 7}}]}, "6": {"line": 88, "type": "if", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 8}}, {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 8}}]}, "7": {"line": 90, "type": "if", "locations": [{"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 10}}, {"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 10}}]}, "8": {"line": 97, "type": "if", "locations": [{"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}, {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}]}, "9": {"line": 105, "type": "cond-expr", "locations": [{"start": {"line": 105, "column": 24}, "end": {"line": 105, "column": 38}}, {"start": {"line": 105, "column": 42}, "end": {"line": 105, "column": 54}}]}, "10": {"line": 124, "type": "if", "locations": [{"start": {"line": 124, "column": 2}, "end": {"line": 124, "column": 2}}, {"start": {"line": 124, "column": 2}, "end": {"line": 124, "column": 2}}]}, "11": {"line": 169, "type": "if", "locations": [{"start": {"line": 169, "column": 2}, "end": {"line": 169, "column": 2}}, {"start": {"line": 169, "column": 2}, "end": {"line": 169, "column": 2}}]}, "12": {"line": 169, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 6}, "end": {"line": 169, "column": 11}}, {"start": {"line": 169, "column": 15}, "end": {"line": 169, "column": 30}}]}, "13": {"line": 174, "type": "cond-expr", "locations": [{"start": {"line": 174, "column": 26}, "end": {"line": 174, "column": 32}}, {"start": {"line": 174, "column": 35}, "end": {"line": 174, "column": 69}}]}, "14": {"line": 174, "type": "cond-expr", "locations": [{"start": {"line": 174, "column": 53}, "end": {"line": 174, "column": 56}}, {"start": {"line": 174, "column": 59}, "end": {"line": 174, "column": 69}}]}, "15": {"line": 199, "type": "if", "locations": [{"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 4}}, {"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 4}}]}, "16": {"line": 199, "type": "binary-expr", "locations": [{"start": {"line": 199, "column": 8}, "end": {"line": 199, "column": 18}}, {"start": {"line": 199, "column": 23}, "end": {"line": 199, "column": 51}}]}, "17": {"line": 204, "type": "binary-expr", "locations": [{"start": {"line": 204, "column": 7}, "end": {"line": 204, "column": 20}}, {"start": {"line": 204, "column": 24}, "end": {"line": 204, "column": 39}}]}, "18": {"line": 209, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 18}, "end": {"line": 209, "column": 25}}, {"start": {"line": 209, "column": 29}, "end": {"line": 209, "column": 44}}]}, "19": {"line": 235, "type": "if", "locations": [{"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 0}}, {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 0}}]}, "20": {"line": 235, "type": "binary-expr", "locations": [{"start": {"line": 235, "column": 4}, "end": {"line": 235, "column": 31}}, {"start": {"line": 235, "column": 35}, "end": {"line": 235, "column": 49}}]}, "21": {"line": 241, "type": "if", "locations": [{"start": {"line": 241, "column": 7}, "end": {"line": 241, "column": 7}}, {"start": {"line": 241, "column": 7}, "end": {"line": 241, "column": 7}}]}, "22": {"line": 241, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 11}, "end": {"line": 241, "column": 40}}, {"start": {"line": 241, "column": 44}, "end": {"line": 241, "column": 54}}]}}}, "/home/<USER>/git/seedrandom/lib/xor128.js": {"path": "/home/<USER>/git/seedrandom/lib/xor128.js", "s": {"1": 1, "2": 1, "3": 3, "4": 3, "5": 3, "6": 3, "7": 3, "8": 3, "9": 4105327, "10": 4105327, "11": 4105327, "12": 4105327, "13": 4105327, "14": 3, "15": 2, "16": 1, "17": 3, "18": 198, "19": 198, "20": 1, "21": 2, "22": 2, "23": 2, "24": 2, "25": 2, "26": 1, "27": 3, "28": 4102054, "29": 3, "30": 1025, "31": 1025, "32": 1025, "33": 3, "34": 3, "35": 3, "36": 2, "37": 1, "38": 2, "39": 1, "40": 3, "41": 1, "42": 1, "43": 0, "44": 0, "45": 0, "46": 0}, "b": {"1": [2, 1], "2": [3, 2], "3": [2, 1], "4": [1, 1], "5": [1, 0], "6": [1, 1], "7": [0, 0], "8": [0, 0], "9": [1, 1], "10": [1, 0]}, "f": {"1": 1, "2": 3, "3": 4105327, "4": 2, "5": 3, "6": 4102054, "7": 1025, "8": 1, "9": 0}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 4, "loc": {"start": {"line": 4, "column": 1}, "end": {"line": 4, "column": 34}}}, "2": {"name": "XorGen", "line": 6, "loc": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 22}}}, "3": {"name": "(anonymous_3)", "line": 15, "loc": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 23}}}, "4": {"name": "copy", "line": 38, "loc": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 20}}}, "5": {"name": "impl", "line": 46, "loc": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 26}}}, "6": {"name": "(anonymous_6)", "line": 49, "loc": {"start": {"line": 49, "column": 13}, "end": {"line": 49, "column": 24}}}, "7": {"name": "(anonymous_7)", "line": 50, "loc": {"start": {"line": 50, "column": 16}, "end": {"line": 50, "column": 27}}}, "8": {"name": "(anonymous_8)", "line": 62, "loc": {"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 28}}}, "9": {"name": "(anonymous_9)", "line": 70, "loc": {"start": {"line": 70, "column": 9}, "end": {"line": 70, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 4, "column": 0}, "end": {"line": 79, "column": 2}}, "2": {"start": {"line": 6, "column": 0}, "end": {"line": 36, "column": 1}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 30}}, "4": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 11}}, "5": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 11}}, "6": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 11}}, "7": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 11}}, "8": {"start": {"line": 15, "column": 2}, "end": {"line": 21, "column": 4}}, "9": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 32}}, "10": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 16}}, "11": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 16}}, "12": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 16}}, "13": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 49}}, "14": {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, "15": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 16}}, "16": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 20}}, "17": {"start": {"line": 32, "column": 2}, "end": {"line": 35, "column": 3}}, "18": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 38}}, "19": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 14}}, "20": {"start": {"line": 38, "column": 0}, "end": {"line": 44, "column": 1}}, "21": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 12}}, "22": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 12}}, "23": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 12}}, "24": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 12}}, "25": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 11}}, "26": {"start": {"line": 46, "column": 0}, "end": {"line": 65, "column": 1}}, "27": {"start": {"line": 47, "column": 2}, "end": {"line": 49, "column": 68}}, "28": {"start": {"line": 49, "column": 26}, "end": {"line": 49, "column": 65}}, "29": {"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 4}}, "30": {"start": {"line": 51, "column": 4}, "end": {"line": 55, "column": 27}}, "31": {"start": {"line": 52, "column": 6}, "end": {"line": 54, "column": 43}}, "32": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 18}}, "33": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 23}}, "34": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 20}}, "35": {"start": {"line": 60, "column": 2}, "end": {"line": 63, "column": 3}}, "36": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 51}}, "37": {"start": {"line": 61, "column": 35}, "end": {"line": 61, "column": 51}}, "38": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 52}}, "39": {"start": {"line": 62, "column": 30}, "end": {"line": 62, "column": 50}}, "40": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 14}}, "41": {"start": {"line": 67, "column": 0}, "end": {"line": 73, "column": 1}}, "42": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 24}}, "43": {"start": {"line": 69, "column": 7}, "end": {"line": 73, "column": 1}}, "44": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 38}}, "45": {"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 34}}, "46": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 21}}}, "branchMap": {"1": {"line": 23, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 2}}, {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 2}}]}, "2": {"line": 48, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 14}, "end": {"line": 48, "column": 18}}, {"start": {"line": 48, "column": 22}, "end": {"line": 48, "column": 32}}]}, "3": {"line": 60, "type": "if", "locations": [{"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 2}}, {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 2}}]}, "4": {"line": 61, "type": "if", "locations": [{"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 4}}, {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 4}}]}, "5": {"line": 67, "type": "if", "locations": [{"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 0}}, {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 0}}]}, "6": {"line": 67, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 10}}, {"start": {"line": 67, "column": 14}, "end": {"line": 67, "column": 28}}]}, "7": {"line": 69, "type": "if", "locations": [{"start": {"line": 69, "column": 7}, "end": {"line": 69, "column": 7}}, {"start": {"line": 69, "column": 7}, "end": {"line": 69, "column": 7}}]}, "8": {"line": 69, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 11}, "end": {"line": 69, "column": 17}}, {"start": {"line": 69, "column": 21}, "end": {"line": 69, "column": 31}}]}, "9": {"line": 77, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 29}}, {"start": {"line": 77, "column": 33}, "end": {"line": 77, "column": 39}}]}, "10": {"line": 78, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 31}}, {"start": {"line": 78, "column": 35}, "end": {"line": 78, "column": 41}}]}}}, "/home/<USER>/git/seedrandom/lib/xorwow.js": {"path": "/home/<USER>/git/seedrandom/lib/xorwow.js", "s": {"1": 1, "2": 1, "3": 3, "4": 3, "5": 4105327, "6": 4105327, "7": 4105327, "8": 4105327, "9": 4105327, "10": 4105327, "11": 3, "12": 3, "13": 3, "14": 3, "15": 3, "16": 3, "17": 2, "18": 1, "19": 3, "20": 198, "21": 198, "22": 3, "23": 198, "24": 1, "25": 2, "26": 2, "27": 2, "28": 2, "29": 2, "30": 2, "31": 2, "32": 1, "33": 3, "34": 4102054, "35": 3, "36": 1025, "37": 1025, "38": 1025, "39": 3, "40": 3, "41": 3, "42": 2, "43": 1, "44": 2, "45": 1, "46": 3, "47": 1, "48": 1, "49": 0, "50": 0, "51": 0, "52": 0}, "b": {"1": [2, 1], "2": [3, 195], "3": [3, 2], "4": [2, 1], "5": [1, 1], "6": [1, 0], "7": [1, 1], "8": [0, 0], "9": [0, 0], "10": [1, 1], "11": [1, 0]}, "f": {"1": 1, "2": 3, "3": 4105327, "4": 2, "5": 3, "6": 4102054, "7": 1025, "8": 1, "9": 0}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 4, "loc": {"start": {"line": 4, "column": 1}, "end": {"line": 4, "column": 34}}}, "2": {"name": "XorGen", "line": 6, "loc": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 22}}}, "3": {"name": "(anonymous_3)", "line": 10, "loc": {"start": {"line": 10, "column": 12}, "end": {"line": 10, "column": 23}}}, "4": {"name": "copy", "line": 41, "loc": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 20}}}, "5": {"name": "impl", "line": 51, "loc": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 26}}}, "6": {"name": "(anonymous_6)", "line": 54, "loc": {"start": {"line": 54, "column": 13}, "end": {"line": 54, "column": 24}}}, "7": {"name": "(anonymous_7)", "line": 55, "loc": {"start": {"line": 55, "column": 16}, "end": {"line": 55, "column": 27}}}, "8": {"name": "(anonymous_8)", "line": 67, "loc": {"start": {"line": 67, "column": 17}, "end": {"line": 67, "column": 28}}}, "9": {"name": "(anonymous_9)", "line": 75, "loc": {"start": {"line": 75, "column": 9}, "end": {"line": 75, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 4, "column": 0}, "end": {"line": 84, "column": 2}}, "2": {"start": {"line": 6, "column": 0}, "end": {"line": 39, "column": 1}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 30}}, "4": {"start": {"line": 10, "column": 2}, "end": {"line": 15, "column": 4}}, "5": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 34}}, "6": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 16}}, "7": {"start": {"line": 12, "column": 17}, "end": {"line": 12, "column": 29}}, "8": {"start": {"line": 12, "column": 30}, "end": {"line": 12, "column": 42}}, "9": {"start": {"line": 12, "column": 43}, "end": {"line": 12, "column": 55}}, "10": {"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 58}}, "11": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 11}}, "12": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 11}}, "13": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 11}}, "14": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 11}}, "15": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 11}}, "16": {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, "17": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 16}}, "18": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 20}}, "19": {"start": {"line": 32, "column": 2}, "end": {"line": 38, "column": 3}}, "20": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 38}}, "21": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, "22": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 37}}, "23": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 14}}, "24": {"start": {"line": 41, "column": 0}, "end": {"line": 49, "column": 1}}, "25": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 12}}, "26": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 12}}, "27": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 12}}, "28": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 12}}, "29": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 12}}, "30": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 12}}, "31": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 11}}, "32": {"start": {"line": 51, "column": 0}, "end": {"line": 70, "column": 1}}, "33": {"start": {"line": 52, "column": 2}, "end": {"line": 54, "column": 68}}, "34": {"start": {"line": 54, "column": 26}, "end": {"line": 54, "column": 65}}, "35": {"start": {"line": 55, "column": 2}, "end": {"line": 62, "column": 4}}, "36": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 27}}, "37": {"start": {"line": 57, "column": 6}, "end": {"line": 59, "column": 43}}, "38": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 18}}, "39": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 23}}, "40": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 20}}, "41": {"start": {"line": 65, "column": 2}, "end": {"line": 68, "column": 3}}, "42": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 51}}, "43": {"start": {"line": 66, "column": 35}, "end": {"line": 66, "column": 51}}, "44": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 52}}, "45": {"start": {"line": 67, "column": 30}, "end": {"line": 67, "column": 50}}, "46": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 14}}, "47": {"start": {"line": 72, "column": 0}, "end": {"line": 78, "column": 1}}, "48": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 24}}, "49": {"start": {"line": 74, "column": 7}, "end": {"line": 78, "column": 1}}, "50": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 38}}, "51": {"start": {"line": 75, "column": 22}, "end": {"line": 75, "column": 34}}, "52": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 21}}}, "branchMap": {"1": {"line": 23, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 2}}, {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 2}}]}, "2": {"line": 34, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 4}}, {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 4}}]}, "3": {"line": 53, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 14}, "end": {"line": 53, "column": 18}}, {"start": {"line": 53, "column": 22}, "end": {"line": 53, "column": 32}}]}, "4": {"line": 65, "type": "if", "locations": [{"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 2}}, {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 2}}]}, "5": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 4}}, {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 4}}]}, "6": {"line": 72, "type": "if", "locations": [{"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 0}}, {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 0}}]}, "7": {"line": 72, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 10}}, {"start": {"line": 72, "column": 14}, "end": {"line": 72, "column": 28}}]}, "8": {"line": 74, "type": "if", "locations": [{"start": {"line": 74, "column": 7}, "end": {"line": 74, "column": 7}}, {"start": {"line": 74, "column": 7}, "end": {"line": 74, "column": 7}}]}, "9": {"line": 74, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 11}, "end": {"line": 74, "column": 17}}, {"start": {"line": 74, "column": 21}, "end": {"line": 74, "column": 31}}]}, "10": {"line": 82, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 29}}, {"start": {"line": 82, "column": 33}, "end": {"line": 82, "column": 39}}]}, "11": {"line": 83, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 31}}, {"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 41}}]}}}, "/home/<USER>/git/seedrandom/lib/xorshift7.js": {"path": "/home/<USER>/git/seedrandom/lib/xorshift7.js", "s": {"1": 1, "2": 1, "3": 3, "4": 3, "5": 4105897, "6": 4105897, "7": 4105897, "8": 4105897, "9": 4105897, "10": 4105897, "11": 4105897, "12": 4105897, "13": 4105897, "14": 4105897, "15": 4105897, "16": 4105897, "17": 4105897, "18": 4105897, "19": 4105897, "20": 4105897, "21": 1, "22": 3, "23": 3, "24": 2, "25": 1, "26": 1, "27": 6, "28": 3, "29": 16, "30": 3, "31": 3, "32": 2, "33": 1, "34": 3, "35": 3, "36": 3, "37": 768, "38": 3, "39": 1, "40": 2, "41": 2, "42": 2, "43": 1, "44": 3, "45": 0, "46": 3, "47": 4102054, "48": 3, "49": 1025, "50": 1025, "51": 1025, "52": 3, "53": 3, "54": 3, "55": 2, "56": 1, "57": 2, "58": 1, "59": 3, "60": 1, "61": 1, "62": 0, "63": 0, "64": 0, "65": 0}, "b": {"1": [2, 1], "2": [19, 17], "3": [2, 1], "4": [0, 3], "5": [3, 2], "6": [2, 1], "7": [1, 1], "8": [1, 0], "9": [1, 1], "10": [0, 0], "11": [0, 0], "12": [1, 1], "13": [1, 0]}, "f": {"1": 1, "2": 3, "3": 4105897, "4": 3, "5": 2, "6": 3, "7": 4102054, "8": 1025, "9": 1, "10": 0}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 6, "loc": {"start": {"line": 6, "column": 1}, "end": {"line": 6, "column": 34}}}, "2": {"name": "XorGen", "line": 8, "loc": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 22}}}, "3": {"name": "(anonymous_3)", "line": 12, "loc": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 23}}}, "4": {"name": "init", "line": 25, "loc": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 26}}}, "5": {"name": "copy", "line": 56, "loc": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 20}}}, "6": {"name": "impl", "line": 62, "loc": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 26}}}, "7": {"name": "(anonymous_7)", "line": 66, "loc": {"start": {"line": 66, "column": 13}, "end": {"line": 66, "column": 24}}}, "8": {"name": "(anonymous_8)", "line": 67, "loc": {"start": {"line": 67, "column": 16}, "end": {"line": 67, "column": 27}}}, "9": {"name": "(anonymous_9)", "line": 79, "loc": {"start": {"line": 79, "column": 17}, "end": {"line": 79, "column": 28}}}, "10": {"name": "(anonymous_10)", "line": 87, "loc": {"start": {"line": 87, "column": 9}, "end": {"line": 87, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 6, "column": 0}, "end": {"line": 96, "column": 2}}, "2": {"start": {"line": 8, "column": 0}, "end": {"line": 54, "column": 1}}, "3": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 16}}, "4": {"start": {"line": 12, "column": 2}, "end": {"line": 23, "column": 4}}, "5": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 36}}, "6": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 13}}, "7": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 29}}, "8": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": 48}}, "9": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 23}}, "10": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 44}}, "11": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 23}}, "12": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 43}}, "13": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 23}}, "14": {"start": {"line": 18, "column": 24}, "end": {"line": 18, "column": 42}}, "15": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 23}}, "16": {"start": {"line": 19, "column": 24}, "end": {"line": 19, "column": 42}}, "17": {"start": {"line": 19, "column": 43}, "end": {"line": 19, "column": 61}}, "18": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 13}}, "19": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 23}}, "20": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 13}}, "21": {"start": {"line": 25, "column": 2}, "end": {"line": 51, "column": 3}}, "22": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 21}}, "23": {"start": {"line": 28, "column": 4}, "end": {"line": 38, "column": 5}}, "24": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 22}}, "25": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 23}}, "26": {"start": {"line": 34, "column": 6}, "end": {"line": 37, "column": 7}}, "27": {"start": {"line": 35, "column": 8}, "end": {"line": 36, "column": 56}}, "28": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 35}}, "29": {"start": {"line": 40, "column": 25}, "end": {"line": 40, "column": 35}}, "30": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 42}}, "31": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 45}}, "32": {"start": {"line": 42, "column": 16}, "end": {"line": 42, "column": 30}}, "33": {"start": {"line": 42, "column": 36}, "end": {"line": 42, "column": 45}}, "34": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 13}}, "35": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 13}}, "36": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}, "37": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 16}}, "38": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 17}}, "39": {"start": {"line": 56, "column": 0}, "end": {"line": 60, "column": 1}}, "40": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 20}}, "41": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 12}}, "42": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 11}}, "43": {"start": {"line": 62, "column": 0}, "end": {"line": 82, "column": 1}}, "44": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 39}}, "45": {"start": {"line": 63, "column": 20}, "end": {"line": 63, "column": 39}}, "46": {"start": {"line": 64, "column": 2}, "end": {"line": 66, "column": 68}}, "47": {"start": {"line": 66, "column": 26}, "end": {"line": 66, "column": 65}}, "48": {"start": {"line": 67, "column": 2}, "end": {"line": 74, "column": 4}}, "49": {"start": {"line": 68, "column": 4}, "end": {"line": 72, "column": 27}}, "50": {"start": {"line": 69, "column": 6}, "end": {"line": 71, "column": 43}}, "51": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 18}}, "52": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 23}}, "53": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 20}}, "54": {"start": {"line": 77, "column": 2}, "end": {"line": 80, "column": 3}}, "55": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 33}}, "56": {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 33}}, "57": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 52}}, "58": {"start": {"line": 79, "column": 30}, "end": {"line": 79, "column": 50}}, "59": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 14}}, "60": {"start": {"line": 84, "column": 0}, "end": {"line": 90, "column": 1}}, "61": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 24}}, "62": {"start": {"line": 86, "column": 7}, "end": {"line": 90, "column": 1}}, "63": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 38}}, "64": {"start": {"line": 87, "column": 22}, "end": {"line": 87, "column": 34}}, "65": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 24}}}, "branchMap": {"1": {"line": 28, "type": "if", "locations": [{"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 4}}, {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 4}}]}, "2": {"line": 41, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": 21}}, {"start": {"line": 41, "column": 25}, "end": {"line": 41, "column": 35}}]}, "3": {"line": 42, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 4}}, {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 4}}]}, "4": {"line": 63, "type": "if", "locations": [{"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 2}}, {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 2}}]}, "5": {"line": 65, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 14}, "end": {"line": 65, "column": 18}}, {"start": {"line": 65, "column": 22}, "end": {"line": 65, "column": 32}}]}, "6": {"line": 77, "type": "if", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 2}}, {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 2}}]}, "7": {"line": 78, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 4}}, {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 4}}]}, "8": {"line": 84, "type": "if", "locations": [{"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 0}}, {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 0}}]}, "9": {"line": 84, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 10}}, {"start": {"line": 84, "column": 14}, "end": {"line": 84, "column": 28}}]}, "10": {"line": 86, "type": "if", "locations": [{"start": {"line": 86, "column": 7}, "end": {"line": 86, "column": 7}}, {"start": {"line": 86, "column": 7}, "end": {"line": 86, "column": 7}}]}, "11": {"line": 86, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 11}, "end": {"line": 86, "column": 17}}, {"start": {"line": 86, "column": 21}, "end": {"line": 86, "column": 31}}]}, "12": {"line": 94, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 29}}, {"start": {"line": 94, "column": 33}, "end": {"line": 94, "column": 39}}]}, "13": {"line": 95, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 31}}, {"start": {"line": 95, "column": 35}, "end": {"line": 95, "column": 41}}]}}}, "/home/<USER>/git/seedrandom/lib/xor4096.js": {"path": "/home/<USER>/git/seedrandom/lib/xor4096.js", "s": {"1": 1, "2": 1, "3": 3, "4": 3, "5": 4105129, "6": 4105129, "7": 4105129, "8": 4105129, "9": 4105129, "10": 4105129, "11": 4105129, "12": 4105129, "13": 4105129, "14": 4105129, "15": 4105129, "16": 1, "17": 3, "18": 3, "19": 2, "20": 2, "21": 1, "22": 1, "23": 1, "24": 3, "25": 480, "26": 160, "27": 480, "28": 3, "29": 480, "30": 480, "31": 480, "32": 480, "33": 480, "34": 384, "35": 384, "36": 384, "37": 3, "38": 0, "39": 3, "40": 3, "41": 1536, "42": 1536, "43": 1536, "44": 1536, "45": 1536, "46": 1536, "47": 1536, "48": 3, "49": 3, "50": 3, "51": 3, "52": 1, "53": 2, "54": 2, "55": 2, "56": 2, "57": 1, "58": 3, "59": 0, "60": 3, "61": 4102054, "62": 3, "63": 1025, "64": 1025, "65": 1025, "66": 3, "67": 3, "68": 3, "69": 2, "70": 1, "71": 2, "72": 1, "73": 3, "74": 1, "75": 1, "76": 0, "77": 0, "78": 0, "79": 0}, "b": {"1": [2, 1], "2": [160, 320], "3": [3, 477], "4": [384, 96], "5": [0, 384], "6": [0, 3], "7": [0, 0, 0], "8": [0, 3], "9": [3, 2], "10": [2, 1], "11": [1, 1], "12": [1, 0], "13": [1, 1], "14": [0, 0], "15": [0, 0], "16": [1, 1], "17": [1, 0]}, "f": {"1": 1, "2": 3, "3": 4105129, "4": 3, "5": 2, "6": 3, "7": 4102054, "8": 1025, "9": 1, "10": 0}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 26, "loc": {"start": {"line": 26, "column": 1}, "end": {"line": 26, "column": 34}}}, "2": {"name": "XorGen", "line": 28, "loc": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 22}}}, "3": {"name": "(anonymous_3)", "line": 32, "loc": {"start": {"line": 32, "column": 12}, "end": {"line": 32, "column": 23}}}, "4": {"name": "init", "line": 51, "loc": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 26}}}, "5": {"name": "copy", "line": 105, "loc": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 20}}}, "6": {"name": "impl", "line": 112, "loc": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 26}}}, "7": {"name": "(anonymous_7)", "line": 116, "loc": {"start": {"line": 116, "column": 13}, "end": {"line": 116, "column": 24}}}, "8": {"name": "(anonymous_8)", "line": 117, "loc": {"start": {"line": 117, "column": 16}, "end": {"line": 117, "column": 27}}}, "9": {"name": "(anonymous_9)", "line": 129, "loc": {"start": {"line": 129, "column": 17}, "end": {"line": 129, "column": 28}}}, "10": {"name": "(anonymous_10)", "line": 137, "loc": {"start": {"line": 137, "column": 9}, "end": {"line": 137, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 26, "column": 0}, "end": {"line": 146, "column": 2}}, "2": {"start": {"line": 28, "column": 0}, "end": {"line": 103, "column": 1}}, "3": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 16}}, "4": {"start": {"line": 32, "column": 2}, "end": {"line": 49, "column": 4}}, "5": {"start": {"line": 33, "column": 4}, "end": {"line": 34, "column": 33}}, "6": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 36}}, "7": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 26}}, "8": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 31}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 17}}, "10": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 17}}, "11": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 18}}, "12": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 18}}, "13": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 21}}, "14": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 13}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 38}}, "16": {"start": {"line": 51, "column": 2}, "end": {"line": 100, "column": 3}}, "17": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 43}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 62, "column": 5}}, "19": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 15}}, "20": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 18}}, "21": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 25}}, "22": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 12}}, "23": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 43}}, "24": {"start": {"line": 64, "column": 4}, "end": {"line": 78, "column": 5}}, "25": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 61}}, "26": {"start": {"line": 66, "column": 16}, "end": {"line": 66, "column": 61}}, "27": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 25}}, "28": {"start": {"line": 68, "column": 19}, "end": {"line": 68, "column": 25}}, "29": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 19}}, "30": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 20}}, "31": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 18}}, "32": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 20}}, "33": {"start": {"line": 73, "column": 6}, "end": {"line": 77, "column": 7}}, "34": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 33}}, "35": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 36}}, "36": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 33}}, "37": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "38": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 47}}, "39": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 12}}, "40": {"start": {"line": 87, "column": 4}, "end": {"line": 95, "column": 5}}, "41": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 28}}, "42": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 33}}, "43": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 19}}, "44": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 19}}, "45": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 20}}, "46": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 20}}, "47": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 19}}, "48": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 13}}, "49": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 13}}, "50": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 13}}, "51": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 17}}, "52": {"start": {"line": 105, "column": 0}, "end": {"line": 110, "column": 1}}, "53": {"start": {"line": 106, "column": 2}, "end": {"line": 106, "column": 12}}, "54": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 12}}, "55": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 20}}, "56": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 11}}, "57": {"start": {"line": 112, "column": 0}, "end": {"line": 132, "column": 1}}, "58": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 39}}, "59": {"start": {"line": 113, "column": 20}, "end": {"line": 113, "column": 39}}, "60": {"start": {"line": 114, "column": 2}, "end": {"line": 116, "column": 68}}, "61": {"start": {"line": 116, "column": 26}, "end": {"line": 116, "column": 65}}, "62": {"start": {"line": 117, "column": 2}, "end": {"line": 124, "column": 4}}, "63": {"start": {"line": 118, "column": 4}, "end": {"line": 122, "column": 27}}, "64": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 43}}, "65": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 18}}, "66": {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 23}}, "67": {"start": {"line": 126, "column": 2}, "end": {"line": 126, "column": 20}}, "68": {"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 3}}, "69": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 33}}, "70": {"start": {"line": 128, "column": 17}, "end": {"line": 128, "column": 33}}, "71": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 52}}, "72": {"start": {"line": 129, "column": 30}, "end": {"line": 129, "column": 50}}, "73": {"start": {"line": 131, "column": 2}, "end": {"line": 131, "column": 14}}, "74": {"start": {"line": 134, "column": 0}, "end": {"line": 140, "column": 1}}, "75": {"start": {"line": 135, "column": 2}, "end": {"line": 135, "column": 24}}, "76": {"start": {"line": 136, "column": 7}, "end": {"line": 140, "column": 1}}, "77": {"start": {"line": 137, "column": 2}, "end": {"line": 137, "column": 38}}, "78": {"start": {"line": 137, "column": 22}, "end": {"line": 137, "column": 34}}, "79": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 22}}}, "branchMap": {"1": {"line": 53, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 4}}, {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 4}}]}, "2": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 6}}, {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 6}}]}, "3": {"line": 68, "type": "if", "locations": [{"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 6}}, {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 6}}]}, "4": {"line": 73, "type": "if", "locations": [{"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 6}}, {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 6}}]}, "5": {"line": 76, "type": "cond-expr", "locations": [{"start": {"line": 76, "column": 23}, "end": {"line": 76, "column": 28}}, {"start": {"line": 76, "column": 31}, "end": {"line": 76, "column": 32}}]}, "6": {"line": 80, "type": "if", "locations": [{"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 4}}, {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 4}}]}, "7": {"line": 81, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 9}, "end": {"line": 81, "column": 13}}, {"start": {"line": 81, "column": 17}, "end": {"line": 81, "column": 28}}, {"start": {"line": 81, "column": 32}, "end": {"line": 81, "column": 33}}]}, "8": {"line": 113, "type": "if", "locations": [{"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 2}}, {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 2}}]}, "9": {"line": 115, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 14}, "end": {"line": 115, "column": 18}}, {"start": {"line": 115, "column": 22}, "end": {"line": 115, "column": 32}}]}, "10": {"line": 127, "type": "if", "locations": [{"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 2}}, {"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 2}}]}, "11": {"line": 128, "type": "if", "locations": [{"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 4}}, {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 4}}]}, "12": {"line": 134, "type": "if", "locations": [{"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 0}}, {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 0}}]}, "13": {"line": 134, "type": "binary-expr", "locations": [{"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 10}}, {"start": {"line": 134, "column": 14}, "end": {"line": 134, "column": 28}}]}, "14": {"line": 136, "type": "if", "locations": [{"start": {"line": 136, "column": 7}, "end": {"line": 136, "column": 7}}, {"start": {"line": 136, "column": 7}, "end": {"line": 136, "column": 7}}]}, "15": {"line": 136, "type": "binary-expr", "locations": [{"start": {"line": 136, "column": 11}, "end": {"line": 136, "column": 17}}, {"start": {"line": 136, "column": 21}, "end": {"line": 136, "column": 31}}]}, "16": {"line": 144, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 29}}, {"start": {"line": 144, "column": 33}, "end": {"line": 144, "column": 39}}]}, "17": {"line": 145, "type": "binary-expr", "locations": [{"start": {"line": 145, "column": 2}, "end": {"line": 145, "column": 31}}, {"start": {"line": 145, "column": 35}, "end": {"line": 145, "column": 41}}]}}}, "/home/<USER>/git/seedrandom/lib/tychei.js": {"path": "/home/<USER>/git/seedrandom/lib/tychei.js", "s": {"1": 1, "2": 1, "3": 3, "4": 3, "5": 4105195, "6": 4105195, "7": 4105195, "8": 4105195, "9": 4105195, "10": 4105195, "11": 4105195, "12": 4105195, "13": 4105195, "14": 3, "15": 3, "16": 3, "17": 3, "18": 3, "19": 2, "20": 2, "21": 1, "22": 3, "23": 66, "24": 66, "25": 1, "26": 2, "27": 2, "28": 2, "29": 2, "30": 2, "31": 1, "32": 3, "33": 4102054, "34": 3, "35": 1025, "36": 1025, "37": 1025, "38": 3, "39": 3, "40": 3, "41": 2, "42": 1, "43": 2, "44": 1, "45": 3, "46": 1, "47": 1, "48": 0, "49": 0, "50": 0, "51": 0}, "b": {"1": [2, 1], "2": [3, 2], "3": [2, 1], "4": [1, 1], "5": [1, 0], "6": [1, 1], "7": [0, 0], "8": [0, 0], "9": [1, 1], "10": [1, 0]}, "f": {"1": 1, "2": 3, "3": 4105195, "4": 2, "5": 3, "6": 4102054, "7": 1025, "8": 1, "9": 0}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 5, "loc": {"start": {"line": 5, "column": 1}, "end": {"line": 5, "column": 34}}}, "2": {"name": "XorGen", "line": 7, "loc": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 22}}}, "3": {"name": "(anonymous_3)", "line": 11, "loc": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 23}}}, "4": {"name": "copy", "line": 60, "loc": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 20}}}, "5": {"name": "impl", "line": 68, "loc": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 26}}}, "6": {"name": "(anonymous_6)", "line": 71, "loc": {"start": {"line": 71, "column": 13}, "end": {"line": 71, "column": 24}}}, "7": {"name": "(anonymous_7)", "line": 72, "loc": {"start": {"line": 72, "column": 16}, "end": {"line": 72, "column": 27}}}, "8": {"name": "(anonymous_8)", "line": 84, "loc": {"start": {"line": 84, "column": 17}, "end": {"line": 84, "column": 28}}}, "9": {"name": "(anonymous_9)", "line": 92, "loc": {"start": {"line": 92, "column": 9}, "end": {"line": 92, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 5, "column": 0}, "end": {"line": 101, "column": 2}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 58, "column": 1}}, "3": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 30}}, "4": {"start": {"line": 11, "column": 2}, "end": {"line": 21, "column": 4}}, "5": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 47}}, "6": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 34}}, "7": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 20}}, "8": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 34}}, "9": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 20}}, "10": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 42}}, "11": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 27}}, "12": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 38}}, "13": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 30}}, "14": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 11}}, "15": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 11}}, "16": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 24}}, "17": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 20}}, "18": {"start": {"line": 44, "column": 2}, "end": {"line": 51, "column": 3}}, "19": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 36}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 20}}, "21": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 20}}, "22": {"start": {"line": 54, "column": 2}, "end": {"line": 57, "column": 3}}, "23": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 38}}, "24": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 14}}, "25": {"start": {"line": 60, "column": 0}, "end": {"line": 66, "column": 1}}, "26": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 12}}, "27": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 12}}, "28": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 12}}, "29": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 12}}, "30": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 11}}, "31": {"start": {"line": 68, "column": 0}, "end": {"line": 87, "column": 1}}, "32": {"start": {"line": 69, "column": 2}, "end": {"line": 71, "column": 68}}, "33": {"start": {"line": 71, "column": 26}, "end": {"line": 71, "column": 65}}, "34": {"start": {"line": 72, "column": 2}, "end": {"line": 79, "column": 4}}, "35": {"start": {"line": 73, "column": 4}, "end": {"line": 77, "column": 27}}, "36": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 43}}, "37": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 18}}, "38": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 23}}, "39": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 20}}, "40": {"start": {"line": 82, "column": 2}, "end": {"line": 85, "column": 3}}, "41": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 51}}, "42": {"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 51}}, "43": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 52}}, "44": {"start": {"line": 84, "column": 30}, "end": {"line": 84, "column": 50}}, "45": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 14}}, "46": {"start": {"line": 89, "column": 0}, "end": {"line": 95, "column": 1}}, "47": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 24}}, "48": {"start": {"line": 91, "column": 7}, "end": {"line": 95, "column": 1}}, "49": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 38}}, "50": {"start": {"line": 92, "column": 22}, "end": {"line": 92, "column": 34}}, "51": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 21}}}, "branchMap": {"1": {"line": 44, "type": "if", "locations": [{"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 2}}, {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 2}}]}, "2": {"line": 70, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 14}, "end": {"line": 70, "column": 18}}, {"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 32}}]}, "3": {"line": 82, "type": "if", "locations": [{"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 2}}, {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 2}}]}, "4": {"line": 83, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 4}}, {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 4}}]}, "5": {"line": 89, "type": "if", "locations": [{"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 0}}, {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 0}}]}, "6": {"line": 89, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 10}}, {"start": {"line": 89, "column": 14}, "end": {"line": 89, "column": 28}}]}, "7": {"line": 91, "type": "if", "locations": [{"start": {"line": 91, "column": 7}, "end": {"line": 91, "column": 7}}, {"start": {"line": 91, "column": 7}, "end": {"line": 91, "column": 7}}]}, "8": {"line": 91, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 11}, "end": {"line": 91, "column": 17}}, {"start": {"line": 91, "column": 21}, "end": {"line": 91, "column": 31}}]}, "9": {"line": 99, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 29}}, {"start": {"line": 99, "column": 33}, "end": {"line": 99, "column": 39}}]}, "10": {"line": 100, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 2}, "end": {"line": 100, "column": 31}}, {"start": {"line": 100, "column": 35}, "end": {"line": 100, "column": 41}}]}}}, "/home/<USER>/git/seedrandom/lib/alea.js": {"path": "/home/<USER>/git/seedrandom/lib/alea.js", "s": {"1": 1, "2": 1, "3": 3, "4": 3, "5": 4105129, "6": 4105129, "7": 4105129, "8": 4105129, "9": 3, "10": 3, "11": 3, "12": 3, "13": 3, "14": 3, "15": 1, "16": 3, "17": 3, "18": 3, "19": 3, "20": 3, "21": 0, "22": 3, "23": 1, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 1, "30": 3, "31": 3, "32": 1025, "33": 3, "34": 1025, "35": 3, "36": 3, "37": 2, "38": 1, "39": 2, "40": 1, "41": 3, "42": 1, "43": 3, "44": 3, "45": 18, "46": 18, "47": 33, "48": 33, "49": 33, "50": 33, "51": 33, "52": 33, "53": 33, "54": 33, "55": 18, "56": 3, "57": 1, "58": 1, "59": 0, "60": 0, "61": 0, "62": 0}, "b": {"1": [1, 2], "2": [3, 0], "3": [0, 3], "4": [3, 2], "5": [2, 1], "6": [1, 1], "7": [1, 0], "8": [1, 1], "9": [0, 0], "10": [0, 0], "11": [1, 1], "12": [1, 0]}, "f": {"1": 1, "2": 3, "3": 4105129, "4": 2, "5": 3, "6": 1025, "7": 1025, "8": 1, "9": 3, "10": 18, "11": 0}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 28, "loc": {"start": {"line": 28, "column": 1}, "end": {"line": 28, "column": 34}}}, "2": {"name": "Alea", "line": 30, "loc": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 20}}}, "3": {"name": "(anonymous_3)", "line": 33, "loc": {"start": {"line": 33, "column": 12}, "end": {"line": 33, "column": 23}}}, "4": {"name": "copy", "line": 54, "loc": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 20}}}, "5": {"name": "impl", "line": 62, "loc": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 26}}}, "6": {"name": "(anonymous_6)", "line": 66, "loc": {"start": {"line": 66, "column": 15}, "end": {"line": 66, "column": 26}}}, "7": {"name": "(anonymous_7)", "line": 67, "loc": {"start": {"line": 67, "column": 16}, "end": {"line": 67, "column": 27}}}, "8": {"name": "(anonymous_8)", "line": 73, "loc": {"start": {"line": 73, "column": 17}, "end": {"line": 73, "column": 28}}}, "9": {"name": "<PERSON><PERSON>", "line": 78, "loc": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 16}}}, "10": {"name": "(anonymous_10)", "line": 81, "loc": {"start": {"line": 81, "column": 13}, "end": {"line": 81, "column": 28}}}, "11": {"name": "(anonymous_11)", "line": 103, "loc": {"start": {"line": 103, "column": 9}, "end": {"line": 103, "column": 20}}}}, "statementMap": {"1": {"start": {"line": 28, "column": 0}, "end": {"line": 112, "column": 2}}, "2": {"start": {"line": 30, "column": 0}, "end": {"line": 52, "column": 1}}, "3": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 31}}, "4": {"start": {"line": 33, "column": 2}, "end": {"line": 38, "column": 4}}, "5": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 60}}, "6": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 18}}, "7": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 18}}, "8": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 38}}, "9": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 11}}, "10": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 20}}, "11": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 20}}, "12": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 20}}, "13": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 22}}, "14": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 32}}, "15": {"start": {"line": 46, "column": 19}, "end": {"line": 46, "column": 30}}, "16": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 22}}, "17": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 32}}, "18": {"start": {"line": 48, "column": 19}, "end": {"line": 48, "column": 30}}, "19": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 22}}, "20": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 32}}, "21": {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 30}}, "22": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 14}}, "23": {"start": {"line": 54, "column": 0}, "end": {"line": 60, "column": 1}}, "24": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 12}}, "25": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 14}}, "26": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 14}}, "27": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 14}}, "28": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 11}}, "29": {"start": {"line": 62, "column": 0}, "end": {"line": 76, "column": 1}}, "30": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 21}}, "31": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 67}}, "32": {"start": {"line": 66, "column": 28}, "end": {"line": 66, "column": 65}}, "33": {"start": {"line": 67, "column": 2}, "end": {"line": 69, "column": 4}}, "34": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 69}}, "35": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 20}}, "36": {"start": {"line": 71, "column": 2}, "end": {"line": 74, "column": 3}}, "37": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 51}}, "38": {"start": {"line": 72, "column": 35}, "end": {"line": 72, "column": 51}}, "39": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 52}}, "40": {"start": {"line": 73, "column": 30}, "end": {"line": 73, "column": 50}}, "41": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 14}}, "42": {"start": {"line": 78, "column": 0}, "end": {"line": 97, "column": 1}}, "43": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 21}}, "44": {"start": {"line": 81, "column": 2}, "end": {"line": 94, "column": 4}}, "45": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 24}}, "46": {"start": {"line": 83, "column": 4}, "end": {"line": 92, "column": 5}}, "47": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 30}}, "48": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 38}}, "49": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 18}}, "50": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 13}}, "51": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 13}}, "52": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 18}}, "53": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 13}}, "54": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 27}}, "55": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 46}}, "56": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 14}}, "57": {"start": {"line": 100, "column": 0}, "end": {"line": 106, "column": 1}}, "58": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 24}}, "59": {"start": {"line": 102, "column": 7}, "end": {"line": 106, "column": 1}}, "60": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 38}}, "61": {"start": {"line": 103, "column": 22}, "end": {"line": 103, "column": 34}}, "62": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": 19}}}, "branchMap": {"1": {"line": 46, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 2}}, {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 2}}]}, "2": {"line": 48, "type": "if", "locations": [{"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 2}}, {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 2}}]}, "3": {"line": 50, "type": "if", "locations": [{"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 2}}, {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 2}}]}, "4": {"line": 64, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 14}, "end": {"line": 64, "column": 18}}, {"start": {"line": 64, "column": 22}, "end": {"line": 64, "column": 32}}]}, "5": {"line": 71, "type": "if", "locations": [{"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 2}}, {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 2}}]}, "6": {"line": 72, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 4}}, {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 4}}]}, "7": {"line": 100, "type": "if", "locations": [{"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 0}}, {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 0}}]}, "8": {"line": 100, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 10}}, {"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 28}}]}, "9": {"line": 102, "type": "if", "locations": [{"start": {"line": 102, "column": 7}, "end": {"line": 102, "column": 7}}, {"start": {"line": 102, "column": 7}, "end": {"line": 102, "column": 7}}]}, "10": {"line": 102, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 11}, "end": {"line": 102, "column": 17}}, {"start": {"line": 102, "column": 21}, "end": {"line": 102, "column": 31}}]}, "11": {"line": 110, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 29}}, {"start": {"line": 110, "column": 33}, "end": {"line": 110, "column": 39}}]}, "12": {"line": 111, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 31}}, {"start": {"line": 111, "column": 35}, "end": {"line": 111, "column": 41}}]}}}}