{"version": 3, "file": "index_with_polyfills.js", "sourceRoot": "", "sources": ["../src/index_with_polyfills.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,gFAAgF;AAChF,OAAO,gBAAgB,CAAC;AACxB,OAAO,6BAA6B,CAAC;AACrC,cAAc,SAAS,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// This file is used to build a union bundle that has polyfills for ES5 support.\nimport 'core-js/stable';\nimport 'regenerator-runtime/runtime';\nexport * from './index';\n"]}