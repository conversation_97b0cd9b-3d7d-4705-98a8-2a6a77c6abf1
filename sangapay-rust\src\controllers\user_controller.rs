use actix_web::{web, HttpResponse, Responder, HttpRequest};
use log::{error, info, warn};
use mongodb::bson::{doc, oid::ObjectId};
use serde::{Deserialize, Serialize};
use serde_json::json;
use validator::Validate;
use chrono::Utc;
use backoff::{ExponentialBackoff, future::retry};
use tower::ServiceBuilder;
use std::time::Duration;

use crate::AppState;
use crate::models::user::User;
use crate::models::account::Account;
use crate::services::token::DecodedToken;
use crate::utils::security::{sanitize_input, hash_password, verify_password};

/// User controller for handling user-related operations
pub struct UserController;

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateUserRequest {
    #[serde(rename = "firstName")]
    #[validate(length(min = 1, message = "First name is required"))]
    pub first_name: String,

    #[serde(rename = "lastName")]
    #[validate(length(min = 1, message = "Last name is required"))]
    pub last_name: String,

    #[validate(email(message = "Invalid email format"))]
    pub email: String,

    #[validate(length(min = 8, message = "Password must be at least 8 characters"))]
    pub password: String,

    #[validate(length(min = 10, message = "Phone number must be at least 10 characters"))]
    pub phone: String,

    #[serde(rename = "dateOfBirth")]
    pub date_of_birth: Option<String>,

    pub country: Option<String>,
    pub state: Option<String>,
    pub city: Option<String>,
    pub address: Option<String>,

    #[serde(rename = "referralCode")]
    pub referral_code: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdateProfileRequest {
    #[serde(rename = "firstName")]
    pub first_name: Option<String>,

    #[serde(rename = "lastName")]
    pub last_name: Option<String>,

    pub phone: Option<String>,

    #[serde(rename = "dateOfBirth")]
    pub date_of_birth: Option<String>,

    pub country: Option<String>,
    pub state: Option<String>,
    pub city: Option<String>,
    pub address: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct ChangePasswordRequest {
    #[serde(rename = "currentPassword")]
    #[validate(length(min = 1, message = "Current password is required"))]
    pub current_password: String,

    #[serde(rename = "newPassword")]
    #[validate(length(min = 8, message = "New password must be at least 8 characters"))]
    pub new_password: String,
}

impl UserController {
    /// Create a new user account
    pub async fn create_user(
        data: web::Data<AppState>,
        req: web::Json<CreateUserRequest>,
        http_req: HttpRequest,
    ) -> impl Responder {
        let start_time = std::time::Instant::now();
        info!("User creation request received");

        // Validate input
        if let Err(validation_errors) = req.validate() {
            return HttpResponse::BadRequest().json(json!({
                "status": false,
                "message": "VALIDATION_ERROR",
                "meta": {
                    "error": "Input validation failed",
                    "details": validation_errors.to_string(),
                    "suggestions": ["Check all required fields and formats"]
                }
            }));
        }

        // Sanitize inputs
        let first_name = sanitize_input(&req.first_name);
        let last_name = sanitize_input(&req.last_name);
        let email = sanitize_input(&req.email.to_lowercase());
        let phone = sanitize_input(&req.phone);

        // Check if user already exists with retry logic
        let check_existing_user = || async {
            data.db.collection::<User>("users")
                .find_one(doc! {
                    "$or": [
                        { "email": &email },
                        { "phone": &phone }
                    ]
                }, None)
                .await
        };

        let existing_user = match retry(ExponentialBackoff::default(), check_existing_user).await {
            Ok(user) => user,
            Err(e) => {
                error!("Database error checking existing user: {}", e);
                return HttpResponse::InternalServerError().json(json!({
                    "status": false,
                    "message": "DATABASE_ERROR",
                    "meta": {
                        "error": "Failed to check existing user",
                        "suggestions": ["Try again later"]
                    }
                }));
            }
        };

        if existing_user.is_some() {
            return HttpResponse::Conflict().json(json!({
                "status": false,
                "message": "USER_ALREADY_EXISTS",
                "meta": {
                    "error": "User with this email or phone already exists",
                    "suggestions": ["Use a different email or phone number", "Try logging in instead"]
                }
            }));
        }

        // Hash password with enhanced security
        let password_hash = match hash_password(&req.password) {
            Ok(hash) => hash,
            Err(e) => {
                error!("Password hashing failed: {}", e);
                return HttpResponse::InternalServerError().json(json!({
                    "status": false,
                    "message": "INTERNAL_SERVER_ERROR",
                    "meta": {
                        "error": "Failed to process password",
                        "suggestions": ["Try again later"]
                    }
                }));
            }
        };

        // Generate client ID for the user
        let client_id = uuid::Uuid::new_v4().to_string();

        // Create user document
        let now = Utc::now();
        let user_doc = User {
            id: None,
            first_name: first_name.clone(),
            last_name: last_name.clone(),
            email: email.clone(),
            password: password_hash,
            phone: phone.clone(),
            date_of_birth: req.date_of_birth.clone(),
            country: req.country.clone(),
            state: req.state.clone(),
            city: req.city.clone(),
            address: req.address.clone(),
            user_is_verified: false,
            kyc_status: "pending".to_string(),
            client_id: client_id.clone(),
            referral_code: req.referral_code.clone(),
            profile_picture: None,
            two_factor_enabled: false,
            login_attempts: 0,
            last_login: None,
            account_locked_until: None,
            email_verified: false,
            phone_verified: false,
            created_at: now,
            updated_at: now,
        };

        // Insert user with retry logic
        let insert_user = || async {
            data.db.collection::<User>("users")
                .insert_one(&user_doc, None)
                .await
        };

        let insert_result = match retry(ExponentialBackoff::default(), insert_user).await {
            Ok(result) => result,
            Err(e) => {
                error!("Failed to insert user: {}", e);
                return HttpResponse::InternalServerError().json(json!({
                    "status": false,
                    "message": "USER_CREATION_FAILED",
                    "meta": {
                        "error": "Failed to create user account",
                        "suggestions": ["Try again later"]
                    }
                }));
            }
        };

        let user_id = insert_result.inserted_id.as_object_id().unwrap();

        // Create default account for the user
        let account_number = Self::generate_account_number();
        let account_doc = Account {
            id: None,
            user_id: user_id,
            account_number: account_number.clone(),
            account_type: "savings".to_string(),
            currency: "USD".to_string(),
            balance: vec![],
            status: "active".to_string(),
            is_active: true,
            created_at: now,
            updated_at: now,
        };

        // Insert account with retry logic
        let insert_account = || async {
            data.db.collection::<Account>("accounts")
                .insert_one(&account_doc, None)
                .await
        };

        if let Err(e) = retry(ExponentialBackoff::default(), insert_account).await {
            error!("Failed to create default account: {}", e);
            // Try to clean up the user record
            let _ = data.db.collection::<User>("users")
                .delete_one(doc! { "_id": user_id }, None)
                .await;

            return HttpResponse::InternalServerError().json(json!({
                "status": false,
                "message": "ACCOUNT_CREATION_FAILED",
                "meta": {
                    "error": "Failed to create default account",
                    "suggestions": ["Try again later"]
                }
            }));
        }

        let elapsed = start_time.elapsed();
        info!("User created successfully in {:?} ms", elapsed.as_millis());

        // Return success response matching TypeScript format
        HttpResponse::Created().json(json!({
            "status": true,
            "message": "USER_CREATED_SUCCESSFULLY",
            "data": {
                "userId": user_id.to_hex(),
                "firstName": first_name,
                "lastName": last_name,
                "email": email,
                "phone": phone,
                "accountNumber": account_number,
                "clientId": client_id,
                "userIsVerified": false,
                "kycStatus": "pending",
                "createdAt": now.to_rfc3339()
            },
            "meta": {
                "processingTime": elapsed.as_millis(),
                "suggestions": [
                    "Verify your email address to activate your account",
                    "Complete KYC verification to unlock all features"
                ]
            }
        }))
    }

    /// Generate a unique account number
    fn generate_account_number() -> String {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let number: u64 = rng.gen_range(**********..**********);
        number.to_string()
    }

    /// Get user details
    pub async fn get_user_details(
        data: web::Data<AppState>,
        path: web::Path<String>,
        http_req: HttpRequest,
    ) -> impl Responder {
        let user_id = path.into_inner();

        // Get the decoded token from the request extensions
        let extensions = http_req.extensions();
        let decoded_token = match extensions.get::<DecodedToken>() {
            Some(token) => token,
            None => {
                return HttpResponse::Unauthorized().json(json!({
                    "status": false,
                    "message": "UNAUTHORIZED",
                    "meta": {
                        "error": "Authentication required",
                        "suggestions": ["Provide a valid authentication token"]
                    }
                }));
            }
        };

        // Parse user ID
        let user_oid = match ObjectId::parse_str(&user_id) {
            Ok(oid) => oid,
            Err(_) => {
                return HttpResponse::BadRequest().json(json!({
                    "status": false,
                    "message": "INVALID_USER_ID",
                    "meta": {
                        "error": "Invalid user ID format",
                        "suggestions": ["Provide a valid MongoDB ObjectId"]
                    }
                }));
            }
        };

        // Find user with retry logic
        let find_user = || async {
            data.db.collection::<User>("users")
                .find_one(doc! { "_id": user_oid }, None)
                .await
        };

        let user = match retry(ExponentialBackoff::default(), find_user).await {
            Ok(Some(user)) => user,
            Ok(None) => {
                return HttpResponse::NotFound().json(json!({
                    "status": false,
                    "message": "USER_NOT_FOUND",
                    "meta": {
                        "error": "User not found",
                        "suggestions": ["Check the user ID and try again"]
                    }
                }));
            }
            Err(e) => {
                error!("Database error finding user: {}", e);
                return HttpResponse::InternalServerError().json(json!({
                    "status": false,
                    "message": "DATABASE_ERROR",
                    "meta": {
                        "error": "Failed to retrieve user data",
                        "suggestions": ["Try again later"]
                    }
                }));
            }
        };

        // Verify token ownership
        if let Some(client_id) = &decoded_token.client_id {
            if user.client_id != *client_id {
                return HttpResponse::Forbidden().json(json!({
                    "status": false,
                    "message": "FORBIDDEN",
                    "meta": {
                        "error": "Access denied",
                        "suggestions": ["Use your own authentication token"]
                    }
                }));
            }
        }

        // Get user's accounts
        let find_accounts = || async {
            data.db.collection::<Account>("accounts")
                .find(doc! { "userId": user_oid }, None)
                .await
        };

        let accounts_cursor = match retry(ExponentialBackoff::default(), find_accounts).await {
            Ok(cursor) => cursor,
            Err(e) => {
                error!("Database error finding accounts: {}", e);
                return HttpResponse::InternalServerError().json(json!({
                    "status": false,
                    "message": "DATABASE_ERROR",
                    "meta": {
                        "error": "Failed to retrieve account data",
                        "suggestions": ["Try again later"]
                    }
                }));
            }
        };

        let accounts: Vec<Account> = match accounts_cursor.try_collect().await {
            Ok(accounts) => accounts,
            Err(e) => {
                error!("Error collecting accounts: {}", e);
                vec![]
            }
        };

        // Return user details matching TypeScript format
        HttpResponse::Ok().json(json!({
            "status": true,
            "message": "USER_DETAILS_RETRIEVED",
            "data": {
                "userId": user.id.map(|id| id.to_hex()).unwrap_or_default(),
                "firstName": user.first_name,
                "lastName": user.last_name,
                "email": user.email,
                "phone": user.phone,
                "dateOfBirth": user.date_of_birth,
                "country": user.country,
                "state": user.state,
                "city": user.city,
                "address": user.address,
                "userIsVerified": user.user_is_verified,
                "kycStatus": user.kyc_status,
                "emailVerified": user.email_verified,
                "phoneVerified": user.phone_verified,
                "twoFactorEnabled": user.two_factor_enabled,
                "profilePicture": user.profile_picture,
                "accounts": accounts.iter().map(|acc| json!({
                    "accountNumber": acc.account_number,
                    "accountType": acc.account_type,
                    "currency": acc.currency,
                    "status": acc.status,
                    "balance": acc.balance
                })).collect::<Vec<_>>(),
                "createdAt": user.created_at.to_rfc3339(),
                "updatedAt": user.updated_at.to_rfc3339()
            }
        }))
    }

    /// Update user profile
    pub async fn update_profile(
        data: web::Data<AppState>,
        path: web::Path<String>,
        req: web::Json<UpdateProfileRequest>,
        http_req: HttpRequest,
    ) -> impl Responder {
        let user_id = path.into_inner();

        // Get the decoded token from the request extensions
        let extensions = http_req.extensions();
        let decoded_token = match extensions.get::<DecodedToken>() {
            Some(token) => token,
            None => {
                return HttpResponse::Unauthorized().json(json!({
                    "status": false,
                    "message": "UNAUTHORIZED",
                    "meta": {
                        "error": "Authentication required",
                        "suggestions": ["Provide a valid authentication token"]
                    }
                }));
            }
        };

        // Parse user ID
        let user_oid = match ObjectId::parse_str(&user_id) {
            Ok(oid) => oid,
            Err(_) => {
                return HttpResponse::BadRequest().json(json!({
                    "status": false,
                    "message": "INVALID_USER_ID",
                    "meta": {
                        "error": "Invalid user ID format",
                        "suggestions": ["Provide a valid MongoDB ObjectId"]
                    }
                }));
            }
        };

        // Find user first to verify ownership
        let find_user = || async {
            data.db.collection::<User>("users")
                .find_one(doc! { "_id": user_oid }, None)
                .await
        };

        let user = match retry(ExponentialBackoff::default(), find_user).await {
            Ok(Some(user)) => user,
            Ok(None) => {
                return HttpResponse::NotFound().json(json!({
                    "status": false,
                    "message": "USER_NOT_FOUND",
                    "meta": {
                        "error": "User not found",
                        "suggestions": ["Check the user ID and try again"]
                    }
                }));
            }
            Err(e) => {
                error!("Database error finding user: {}", e);
                return HttpResponse::InternalServerError().json(json!({
                    "status": false,
                    "message": "DATABASE_ERROR",
                    "meta": {
                        "error": "Failed to retrieve user data",
                        "suggestions": ["Try again later"]
                    }
                }));
            }
        };

        // Verify token ownership
        if let Some(client_id) = &decoded_token.client_id {
            if user.client_id != *client_id {
                return HttpResponse::Forbidden().json(json!({
                    "status": false,
                    "message": "FORBIDDEN",
                    "meta": {
                        "error": "Access denied",
                        "suggestions": ["Use your own authentication token"]
                    }
                }));
            }
        }

        // Build update document
        let mut update_doc = doc! {
            "$set": {
                "updated_at": Utc::now()
            }
        };

        if let Some(first_name) = &req.first_name {
            update_doc.get_document_mut("$set").unwrap()
                .insert("firstName", sanitize_input(first_name));
        }

        if let Some(last_name) = &req.last_name {
            update_doc.get_document_mut("$set").unwrap()
                .insert("lastName", sanitize_input(last_name));
        }

        if let Some(phone) = &req.phone {
            update_doc.get_document_mut("$set").unwrap()
                .insert("phone", sanitize_input(phone));
        }

        if let Some(date_of_birth) = &req.date_of_birth {
            update_doc.get_document_mut("$set").unwrap()
                .insert("dateOfBirth", date_of_birth);
        }

        if let Some(country) = &req.country {
            update_doc.get_document_mut("$set").unwrap()
                .insert("country", sanitize_input(country));
        }

        if let Some(state) = &req.state {
            update_doc.get_document_mut("$set").unwrap()
                .insert("state", sanitize_input(state));
        }

        if let Some(city) = &req.city {
            update_doc.get_document_mut("$set").unwrap()
                .insert("city", sanitize_input(city));
        }

        if let Some(address) = &req.address {
            update_doc.get_document_mut("$set").unwrap()
                .insert("address", sanitize_input(address));
        }

        // Update user with retry logic
        let update_user = || async {
            data.db.collection::<User>("users")
                .update_one(doc! { "_id": user_oid }, &update_doc, None)
                .await
        };

        match retry(ExponentialBackoff::default(), update_user).await {
            Ok(result) => {
                if result.modified_count == 0 {
                    return HttpResponse::BadRequest().json(json!({
                        "status": false,
                        "message": "UPDATE_FAILED",
                        "meta": {
                            "error": "No changes were made",
                            "suggestions": ["Check if the provided data is different from current values"]
                        }
                    }));
                }

                HttpResponse::Ok().json(json!({
                    "status": true,
                    "message": "PROFILE_UPDATED_SUCCESSFULLY",
                    "data": {
                        "userId": user_id,
                        "updatedAt": Utc::now().to_rfc3339()
                    }
                }))
            }
            Err(e) => {
                error!("Failed to update user profile: {}", e);
                HttpResponse::InternalServerError().json(json!({
                    "status": false,
                    "message": "UPDATE_FAILED",
                    "meta": {
                        "error": "Failed to update profile",
                        "suggestions": ["Try again later"]
                    }
                }))
            }
        }
    }
}
